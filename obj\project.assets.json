{"version": 3, "targets": {"net9.0-windows7.0": {"MQTTnet/4.3.7.1207": {"type": "package", "compile": {"lib/net7.0/MQTTnet.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/MQTTnet.dll": {"related": ".xml"}}}, "NModbus/3.0.81": {"type": "package", "compile": {"lib/net6.0/NModbus.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/NModbus.dll": {"related": ".xml"}}}}}, "libraries": {"MQTTnet/4.3.7.1207": {"sha512": "ah7aHXoedWp5m5a4zy2u4phOEVj0QFYzOb5tFKQeV8RRBrxp+1QREF4ymZuG8D+hzB2dhtrrG81WxTFv0PzOeQ==", "type": "package", "path": "mqttnet/4.3.7.1207", "files": [".nupkg.metadata", ".signature.p7s", "lib/net452/MQTTnet.dll", "lib/net452/MQTTnet.xml", "lib/net461/MQTTnet.dll", "lib/net461/MQTTnet.xml", "lib/net48/MQTTnet.dll", "lib/net48/MQTTnet.xml", "lib/net5.0/MQTTnet.dll", "lib/net5.0/MQTTnet.xml", "lib/net6.0/MQTTnet.dll", "lib/net6.0/MQTTnet.xml", "lib/net7.0/MQTTnet.dll", "lib/net7.0/MQTTnet.xml", "lib/netcoreapp3.1/MQTTnet.dll", "lib/netcoreapp3.1/MQTTnet.xml", "lib/netstandard1.3/MQTTnet.dll", "lib/netstandard1.3/MQTTnet.xml", "lib/netstandard2.0/MQTTnet.dll", "lib/netstandard2.0/MQTTnet.xml", "lib/netstandard2.1/MQTTnet.dll", "lib/netstandard2.1/MQTTnet.xml", "lib/uap10.0.10240/MQTTnet.dll", "lib/uap10.0.10240/MQTTnet.pri", "lib/uap10.0.10240/MQTTnet.xml", "mqttnet.4.3.7.1207.nupkg.sha512", "mqttnet.nuspec", "nuget.png"]}, "NModbus/3.0.81": {"sha512": "EzKEp7CHD8ErBL36iMts+6IrZZ9FEqllaD7Y5XzhoRjlxt5yXRughQ1bxPs99QFYFkW5xfkANB0Qs1gAmYGP8Q==", "type": "package", "path": "nmodbus/3.0.81", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net46/NModbus.dll", "lib/net46/NModbus.xml", "lib/net6.0/NModbus.dll", "lib/net6.0/NModbus.xml", "lib/netstandard1.3/NModbus.dll", "lib/netstandard1.3/NModbus.xml", "lib/netstandard2.0/NModbus.dll", "lib/netstandard2.0/NModbus.xml", "nmodbus.3.0.81.nupkg.sha512", "nmodbus.nuspec"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["MQTTnet >= 4.3.7", "NModbus >= 3.0.81"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS.csproj", "projectName": "ControlDeProducciónENAGAS", "projectPath": "C:\\Users\\<USER>\\source\\repos\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"MQTTnet": {"target": "Package", "version": "[4.3.7, )"}, "NModbus": {"target": "Package", "version": "[3.0.81, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1603", "level": "Warning", "warningLevel": 1, "message": "ControlDeProducciónENAGAS depende de MQTTnet (>= 4.3.7), pero no se encontró MQTTnet 4.3.7. MQTTnet 4.3.7.1207 se resolvió en su lugar.", "libraryId": "MQTTnet", "targetGraphs": ["net9.0-windows7.0"]}]}