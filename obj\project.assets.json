{"version": 3, "targets": {"net9.0-windows7.0": {"EasyModbusTCP/5.6.0": {"type": "package", "compile": {"lib/net40/EasyModbus.dll": {"related": ".xml"}}, "runtime": {"lib/net40/EasyModbus.dll": {"related": ".xml"}}}, "MQTTnet/4.3.7.1207": {"type": "package", "compile": {"lib/net7.0/MQTTnet.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/MQTTnet.dll": {"related": ".xml"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.native.System.IO.Ports/9.0.6": {"type": "package", "dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.6"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-x64"}}}, "System.IO.Ports/9.0.6": {"type": "package", "dependencies": {"runtime.native.System.IO.Ports": "9.0.6"}, "compile": {"lib/net9.0/System.IO.Ports.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.IO.Ports.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/net9.0/System.IO.Ports.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net9.0/System.IO.Ports.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"EasyModbusTCP/5.6.0": {"sha512": "2a5a9smmIu0Tq5zdwMvgO+WDvUgH0JuNv/crAILrDKcNRb8P/AOx9PEyegBiMn1TGXLP3byu0zhsSSUrrVyyXw==", "type": "package", "path": "easymodbustcp/5.6.0", "files": [".nupkg.metadata", ".signature.p7s", "easymodbustcp.5.6.0.nupkg.sha512", "easymodbustcp.nuspec", "lib/net40/EasyModbus.dll", "lib/net40/EasyModbus.xml"]}, "MQTTnet/4.3.7.1207": {"sha512": "ah7aHXoedWp5m5a4zy2u4phOEVj0QFYzOb5tFKQeV8RRBrxp+1QREF4ymZuG8D+hzB2dhtrrG81WxTFv0PzOeQ==", "type": "package", "path": "mqttnet/4.3.7.1207", "files": [".nupkg.metadata", ".signature.p7s", "lib/net452/MQTTnet.dll", "lib/net452/MQTTnet.xml", "lib/net461/MQTTnet.dll", "lib/net461/MQTTnet.xml", "lib/net48/MQTTnet.dll", "lib/net48/MQTTnet.xml", "lib/net5.0/MQTTnet.dll", "lib/net5.0/MQTTnet.xml", "lib/net6.0/MQTTnet.dll", "lib/net6.0/MQTTnet.xml", "lib/net7.0/MQTTnet.dll", "lib/net7.0/MQTTnet.xml", "lib/netcoreapp3.1/MQTTnet.dll", "lib/netcoreapp3.1/MQTTnet.xml", "lib/netstandard1.3/MQTTnet.dll", "lib/netstandard1.3/MQTTnet.xml", "lib/netstandard2.0/MQTTnet.dll", "lib/netstandard2.0/MQTTnet.xml", "lib/netstandard2.1/MQTTnet.dll", "lib/netstandard2.1/MQTTnet.xml", "lib/uap10.0.10240/MQTTnet.dll", "lib/uap10.0.10240/MQTTnet.pri", "lib/uap10.0.10240/MQTTnet.xml", "mqttnet.4.3.7.1207.nupkg.sha512", "mqttnet.nuspec", "nuget.png"]}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.6": {"sha512": "S1Gu7KIiBM5Zfkve5iaVZUMPToGZ8fc13IMLNdyU20G9nq9LnKSN5e0xb/TFr4N6IqWkAxmTD4JkcWXdjdk33g==", "type": "package", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.android-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/libSystem.IO.Ports.Native.so", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.6": {"sha512": "B5GL3MQcQ4OI02Q3jXdiISS5N7ZI6LCHyDQTpfJTpzTdf4SDwTMMxrcpGaPSth6l7yVyAtJJbIhgdFDw3PmOhg==", "type": "package", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.android-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.6": {"sha512": "7Aq/6BqdFCShOlL+f7ltFCQkwo/YFzZ+wOmK8ObpGfzhxWp2Mg7H4DuMoqd1pO+ikdfbOcDm7cfdMmsUwgbKkQ==", "type": "package", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.android-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/libSystem.IO.Ports.Native.so", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.6": {"sha512": "LG0iGAvkdSWD3WT+oZcozJFZKeINQ160n68YfnwVgtmFpMR2O3GIfuMrf9WJjfnZJb6pbaNnLGqOTpXVJTJa2Q==", "type": "package", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-x86.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.android-x86.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/libSystem.IO.Ports.Native.so", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.6": {"sha512": "RzAA2D77LRuYVTUXKG8wLRbbDF6BA+fHeBtsdar3ARj3cWmqscR3sb5QgROBKtDj1G2Idu3aj+5Bk3TYc+f4XA==", "type": "package", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.linux-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/libSystem.IO.Ports.Native.so", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.6": {"sha512": "a9pVVDsO76cdoGN3CYEgxTE54C5udq9hfOjPKVxyfwOk1N12w18VKL2a15deezFMqjGggaVyK0cFBj9qG7pqWw==", "type": "package", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.linux-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.6": {"sha512": "Kb8CZNgH0aWd8Ks6jS1F286SmVJ4FmJjiUdrQTvHu1aN1cWpfwLZ1qOARvFI3lbXE/geOzBIHDNWmQjyOAeUlg==", "type": "package", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.linux-bionic-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.6": {"sha512": "BCbWsWzSdv9ly9pR1JFY89an+8nc9yZtjAPPlcpH31UUP0AuI27rnrKzcZAuqFXlKy2M8EZVnjV0czSTFueqGA==", "type": "package", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.linux-bionic-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.6": {"sha512": "eapJ2k/eeMK7Q/Mbticy5rgSb+qI884W9Wk6UTDnEdRpd0BvKbhgM845QEmk3vrxT6B8cCr4A8pRseZBdmk4WA==", "type": "package", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.linux-musl-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.6": {"sha512": "+3S2ksd6iFNeyAxtCZ2dGlxXNGQsOgIgGiecu34++UnUTY9KFhkg8T69hyjEMg4+dRQXEWrU4+vP4AI3s5GlMw==", "type": "package", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.linux-musl-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.6": {"sha512": "Fa+EbKuQ6W4EzdVRAA/6ffJ3C0eQ93D8bhmnFaVEHBkfDTKNUSZKhjLdYgubvMrSQlsQ8XLGw0Ld1UXMgGCj7w==", "type": "package", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.linux-musl-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.6": {"sha512": "POgI6/WVHtpxbqvfFqVybZckRbgFVp3nE0fOBpIQdKiZ9C3MPKKibyFNEBK81ZlgmtTEpJP0jMvLSuEbA/p95g==", "type": "package", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.linux-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/libSystem.IO.Ports.Native.so", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.6": {"sha512": "VMJ5KlYwc2OUPEXmuMetGdwU2IKCDH0mYPz+7G0e2psKJ6Q4JVq9VIOK/nnFJ9z0nbw7Cxu5m7nwh8p/ZPr/eA==", "type": "package", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.maccatalyst-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.6": {"sha512": "kbPhYFOoSo7l71lZo4RFx51Vj6BbQAL3QFn4duULbrpV1GEQX5ZrmBSpdxigcvDMit1i/+wDTyMll9t56i/knQ==", "type": "package", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.maccatalyst-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.native.System.IO.Ports/9.0.6": {"sha512": "Q8gHRVpOlTQSEqKtimf7s3lr8OaYaxXcrXjtF78k+6RogQ0BpEHnUgeBZBoQ53qSiztBAzkF22uPOHq+/+goOA==", "type": "package", "path": "runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.native.system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.6": {"sha512": "a+KcHUMoNJG1Z9uvf28HDmT8jK5rm0ExTUdScsdP/ukU2KE7ah+vLxNbh4zCxzvGHsx+Z6bVpaWLjuSYNbqilQ==", "type": "package", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.osx-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.6": {"sha512": "ogySJ/MVq9J/X1ugQOAA4dQfoJSUnZtIbPLr8t2tsaGkV7TBgWOnFInRXy1c20o79M6ARyus12UinDKsFaLkwA==", "type": "package", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512", "runtime.osx-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib", "useSharedDesignerContext.txt"]}, "System.IO.Ports/9.0.6": {"sha512": "D1nZmsZfUfKQ9/AqiAEafmnYijUoJvXtl0RWZ2P+q/Wq3gXgEtp+NzKTpabw2s0aiuPAsdx8SujQY06W2X4ucQ==", "type": "package", "path": "system.io.ports/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Ports.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Ports.targets", "lib/net462/System.IO.Ports.dll", "lib/net462/System.IO.Ports.xml", "lib/net8.0/System.IO.Ports.dll", "lib/net8.0/System.IO.Ports.xml", "lib/net9.0/System.IO.Ports.dll", "lib/net9.0/System.IO.Ports.xml", "lib/netstandard2.0/System.IO.Ports.dll", "lib/netstandard2.0/System.IO.Ports.xml", "runtimes/unix/lib/net8.0/System.IO.Ports.dll", "runtimes/unix/lib/net8.0/System.IO.Ports.xml", "runtimes/unix/lib/net9.0/System.IO.Ports.dll", "runtimes/unix/lib/net9.0/System.IO.Ports.xml", "runtimes/win/lib/net8.0/System.IO.Ports.dll", "runtimes/win/lib/net8.0/System.IO.Ports.xml", "runtimes/win/lib/net9.0/System.IO.Ports.dll", "runtimes/win/lib/net9.0/System.IO.Ports.xml", "system.io.ports.9.0.6.nupkg.sha512", "system.io.ports.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["EasyModbusTCP >= 5.6.0", "MQTTnet >= 4.3.7", "System.IO.Ports >= 9.0.6"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS.csproj", "projectName": "ControlDeProducciónENAGAS", "projectPath": "C:\\Users\\<USER>\\source\\repos\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"EasyModbusTCP": {"target": "Package", "version": "[5.6.0, )"}, "MQTTnet": {"target": "Package", "version": "[4.3.7, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1603", "level": "Warning", "warningLevel": 1, "message": "ControlDeProducciónENAGAS depende de MQTTnet (>= 4.3.7), pero no se encontró MQTTnet 4.3.7. MQTTnet 4.3.7.1207 se resolvió en su lugar.", "libraryId": "MQTTnet", "targetGraphs": ["net9.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "El paquete \"EasyModbusTCP 5.6.0\" se restauró con \".NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1\" en lugar de la plataforma de destino del proyecto \"net9.0-windows7.0\". Puede que el paquete no sea totalmente compatible con el proyecto.", "libraryId": "EasyModbusTCP", "targetGraphs": ["net9.0-windows7.0"]}]}