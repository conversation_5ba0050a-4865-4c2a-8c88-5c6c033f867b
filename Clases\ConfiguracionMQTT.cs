using System;
using System.IO;
using System.Text.Json;

namespace ControlDeProducciónENAGAS.Clases
{
    /// <summary>
    /// Clase para manejar la configuración MQTT desde archivo JSON
    /// </summary>
    public class ConfiguracionMQTT
    {
        public string Name { get; set; } = "";
        public string Protocolo { get; set; } = "mqtt://";
        public string Host { get; set; } = "";
        public int Puerto { get; set; } = 1883;
        public string ClientId { get; set; } = "";
        public string Usuario { get; set; } = "";
        public string Password { get; set; } = "";
        public bool UsarSslTls { get; set; } = false;
        public bool CertificadoCA { get; set; } = false;

        /// <summary>
        /// Carga la configuración desde el archivo JSON
        /// </summary>
        /// <param name="rutaArchivo">Ruta del archivo de configuración</param>
        /// <returns>Configuración MQTT cargada</returns>
        public static ConfiguracionMQTT CargarDesdeArchivo(string rutaArchivo = "config_mqtt.json")
        {
            try
            {
                if (!File.Exists(rutaArchivo))
                {
                    // Crear archivo de configuración por defecto
                    var configDefault = new ConfiguracionMQTT
                    {
                        Name = "ALMENDRALEJO",
                        Protocolo = "mqtts://",
                        Host = "mqtt.greeneaglesolutions.com",
                        Puerto = 8883,
                        ClientId = GenerarClientIdAleatorio(),
                        Usuario = "enagashuelva",
                        Password = "ZQwz6AKVZU8O1iFLH-CC",
                        UsarSslTls = true,
                        CertificadoCA = true
                    };

                    GuardarEnArchivo(configDefault, rutaArchivo);
                    return configDefault;
                }

                string jsonContent = File.ReadAllText(rutaArchivo);
                var jsonDoc = JsonDocument.Parse(jsonContent);
                var mqttSection = jsonDoc.RootElement.GetProperty("mqtt");

                return new ConfiguracionMQTT
                {
                    Name = mqttSection.TryGetProperty("name", out var name) ? name.GetString() ?? "" : "",
                    Protocolo = mqttSection.TryGetProperty("protocolo", out var protocolo) ? protocolo.GetString() ?? "mqtt://" : "mqtt://",
                    Host = mqttSection.TryGetProperty("host", out var host) ? host.GetString() ?? "" : "",
                    Puerto = mqttSection.TryGetProperty("puerto", out var puerto) ? puerto.GetInt32() : 1883,
                    ClientId = mqttSection.TryGetProperty("clientId", out var clientId) ? clientId.GetString() ?? GenerarClientIdAleatorio() : GenerarClientIdAleatorio(),
                    Usuario = mqttSection.TryGetProperty("usuario", out var usuario) ? usuario.GetString() ?? "" : "",
                    Password = mqttSection.TryGetProperty("password", out var password) ? password.GetString() ?? "" : "",
                    UsarSslTls = mqttSection.TryGetProperty("usarSslTls", out var ssl) ? ssl.GetBoolean() : false,
                    CertificadoCA = mqttSection.TryGetProperty("certificadoCA", out var cert) ? cert.GetBoolean() : false
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"Error cargando configuración MQTT: {ex.Message}");
            }
        }

        /// <summary>
        /// Guarda la configuración en archivo JSON
        /// </summary>
        /// <param name="config">Configuración a guardar</param>
        /// <param name="rutaArchivo">Ruta del archivo</param>
        public static void GuardarEnArchivo(ConfiguracionMQTT config, string rutaArchivo = "config_mqtt.json")
        {
            try
            {
                var jsonObject = new
                {
                    mqtt = new
                    {
                        name = config.Name,
                        protocolo = config.Protocolo,
                        host = config.Host,
                        puerto = config.Puerto,
                        clientId = config.ClientId,
                        usuario = config.Usuario,
                        password = config.Password,
                        usarSslTls = config.UsarSslTls,
                        certificadoCA = config.CertificadoCA
                    }
                };

                string jsonString = JsonSerializer.Serialize(jsonObject, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                File.WriteAllText(rutaArchivo, jsonString);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error guardando configuración MQTT: {ex.Message}");
            }
        }

        /// <summary>
        /// Convierte la configuración a ConexionMQTT
        /// </summary>
        /// <returns>Instancia de ConexionMQTT configurada</returns>
        public ConexionMQTT ToConexionMQTT()
        {
            return new ConexionMQTT
            {
                Protocolo = this.Protocolo,
                Host = this.Host,
                Puerto = this.Puerto,
                ClientId = this.ClientId,
                Usuario = this.Usuario,
                Password = this.Password,
                UsarSslTls = this.UsarSslTls
            };
        }

        /// <summary>
        /// Genera un Client ID aleatorio único
        /// </summary>
        /// <returns>Client ID aleatorio</returns>
        private static string GenerarClientIdAleatorio()
        {
            return $"ENAGAS_ALMENDRALEJO_{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
        }

        /// <summary>
        /// Valida que la configuración sea correcta
        /// </summary>
        /// <returns>True si es válida</returns>
        public bool EsValida()
        {
            return !string.IsNullOrWhiteSpace(Host) &&
                   Puerto > 0 && Puerto <= 65535 &&
                   !string.IsNullOrWhiteSpace(ClientId) &&
                   !string.IsNullOrWhiteSpace(Protocolo);
        }

        /// <summary>
        /// Obtiene errores de validación
        /// </summary>
        /// <returns>Lista de errores</returns>
        public List<string> ObtenerErrores()
        {
            var errores = new List<string>();

            if (string.IsNullOrWhiteSpace(Host))
                errores.Add("❌ El host no puede estar vacío");

            if (Puerto <= 0 || Puerto > 65535)
                errores.Add("❌ El puerto debe estar entre 1 y 65535");

            if (string.IsNullOrWhiteSpace(ClientId))
                errores.Add("❌ El Client ID no puede estar vacío");

            if (string.IsNullOrWhiteSpace(Protocolo))
                errores.Add("❌ Debe especificar un protocolo");

            return errores;
        }

        public override string ToString()
        {
            return $"MQTT Config: {Name} - {Protocolo}{Host}:{Puerto} (SSL: {UsarSslTls})";
        }
    }
}
