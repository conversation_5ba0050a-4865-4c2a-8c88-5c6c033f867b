﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace ControlDeProducciónENAGAS.Formularios
{
    partial class MAIN
    {
        /// <summary>
        /// Variable del diseñador necesaria.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Limpiar los recursos que se estén usando.
        /// </summary>
        /// <param name="disposing">true si los recursos administrados se deben desechar; false en caso contrario.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Código generado por el Diseñador de Windows Forms

        /// <summary>
        /// Método necesario para admitir el Diseñador. No se puede modificar
        /// el contenido de este método con el editor de código.
        /// </summary>
        private void InitializeComponent()
        {
            this.panelBarraSuperior = new System.Windows.Forms.Panel();
            this.lblTituloApp = new System.Windows.Forms.Label();
            this.btnCerrar = new System.Windows.Forms.Button();
            this.panelEstados = new System.Windows.Forms.Panel();
            this.panelEstadoMqtt = new System.Windows.Forms.Panel();
            this.lblEstadoMqtt = new System.Windows.Forms.Label();
            this.panelEstadoCorreo = new System.Windows.Forms.Panel();
            this.lblEstadoCorreo = new System.Windows.Forms.Label();
            this.panelEstadoModbus = new System.Windows.Forms.Panel();
            this.lblEstadoModbus = new System.Windows.Forms.Label();
            this.panelNavegacion = new System.Windows.Forms.Panel();
            this.btnNavCorreo = new System.Windows.Forms.Button();
            this.btnNavMqtt = new System.Windows.Forms.Button();
            this.btnNavModbus = new System.Windows.Forms.Button();
            this.btnNavSinoptico = new System.Windows.Forms.Button();
            this.panelLog = new System.Windows.Forms.Panel();
            this.rtbLog = new System.Windows.Forms.RichTextBox();
            this.btnLimpiarLog = new System.Windows.Forms.Button();
            this.tabControlPrincipal = new System.Windows.Forms.TabControl();
            this.tabPageCorreo = new System.Windows.Forms.TabPage();
            this.grpConfigCorreo = new System.Windows.Forms.GroupBox();
            this.lblEwsServerUrl = new System.Windows.Forms.Label();
            this.txtEwsServerUrl = new System.Windows.Forms.TextBox();
            this.lblEwsUsuario = new System.Windows.Forms.Label();
            this.txtEwsUsuario = new System.Windows.Forms.TextBox();
            this.lblEwsPassword = new System.Windows.Forms.Label();
            this.txtEwsPassword = new System.Windows.Forms.TextBox();
            this.tabPageMqtt = new System.Windows.Forms.TabPage();
            this.grpConexionMqtt = new System.Windows.Forms.GroupBox();
            this.lblMqttProtocolo = new System.Windows.Forms.Label();
            this.cmbMqttProtocolo = new System.Windows.Forms.ComboBox();
            this.lblMqttHost = new System.Windows.Forms.Label();
            this.txtMqttHost = new System.Windows.Forms.TextBox();
            this.lblMqttPuerto = new System.Windows.Forms.Label();
            this.txtMqttPuerto = new System.Windows.Forms.TextBox();
            this.lblMqttClientId = new System.Windows.Forms.Label();
            this.txtMqttClientId = new System.Windows.Forms.TextBox();
            this.lblMqttUsuario = new System.Windows.Forms.Label();
            this.txtMqttUsuario = new System.Windows.Forms.TextBox();
            this.lblMqttPassword = new System.Windows.Forms.Label();
            this.txtMqttPassword = new System.Windows.Forms.TextBox();
            this.chkMqttSslTls = new System.Windows.Forms.CheckBox();
            this.btnMqttConectar = new System.Windows.Forms.Button();
            this.btnMqttDesconectar = new System.Windows.Forms.Button();
            this.tabPageModbus = new System.Windows.Forms.TabPage();
            this.grpOperacionesModbus = new System.Windows.Forms.GroupBox();
            this.lblModbusDireccionRegistro = new System.Windows.Forms.Label();
            this.txtModbusDireccionRegistro = new System.Windows.Forms.TextBox();
            this.lblModbusValorEscritura = new System.Windows.Forms.Label();
            this.txtModbusValorEscritura = new System.Windows.Forms.TextBox();
            this.btnModbusLeerRegistros = new System.Windows.Forms.Button();
            this.btnModbusEscribirRegistro = new System.Windows.Forms.Button();
            this.grpConexionModbus = new System.Windows.Forms.GroupBox();
            this.lblModbusIp = new System.Windows.Forms.Label();
            this.txtModbusIp = new System.Windows.Forms.TextBox();
            this.lblModbusPuerto = new System.Windows.Forms.Label();
            this.nudModbusPuerto = new System.Windows.Forms.NumericUpDown();
            this.lblModbusDeviceId = new System.Windows.Forms.Label();
            this.nudModbusDeviceId = new System.Windows.Forms.NumericUpDown();
            this.btnModbusConectar = new System.Windows.Forms.Button();
            this.btnModbusDesconectar = new System.Windows.Forms.Button();
            this.dgvModbusResultados = new System.Windows.Forms.DataGridView();
            this.grpConfigModbus = new System.Windows.Forms.GroupBox();
            this.cmbModbusConfig = new System.Windows.Forms.ComboBox();
            this.txtModbusConfigNombre = new System.Windows.Forms.TextBox();
            this.btnModbusGuardarConfig = new System.Windows.Forms.Button();
            this.btnModbusNuevaConfig = new System.Windows.Forms.Button();
            this.tabPageSinoptico = new System.Windows.Forms.TabPage();
            this.lblTopicosActivos = new System.Windows.Forms.Label();
            this.lblUltimaActualizacion = new System.Windows.Forms.Label();
            this.grpDatosSinoptico = new System.Windows.Forms.GroupBox();
            this.txtSinopticoFiltro = new System.Windows.Forms.TextBox();
            this.btnSinopticoActualizar = new System.Windows.Forms.Button();
            this.dgvSinopticoDatos = new System.Windows.Forms.DataGridView();
            this.panelBarraSuperior.SuspendLayout();
            this.panelEstados.SuspendLayout();
            this.panelNavegacion.SuspendLayout();
            this.panelLog.SuspendLayout();
            this.tabControlPrincipal.SuspendLayout();
            this.tabPageCorreo.SuspendLayout();
            this.grpConfigCorreo.SuspendLayout();
            this.tabPageMqtt.SuspendLayout();
            this.grpConexionMqtt.SuspendLayout();
            this.tabPageModbus.SuspendLayout();
            this.grpOperacionesModbus.SuspendLayout();
            this.grpConexionModbus.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudModbusPuerto)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudModbusDeviceId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvModbusResultados)).BeginInit();
            this.grpConfigModbus.SuspendLayout();
            this.tabPageSinoptico.SuspendLayout();
            this.grpDatosSinoptico.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvSinopticoDatos)).BeginInit();
            this.SuspendLayout();
            // 
            // panelBarraSuperior
            // 
            this.panelBarraSuperior.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(30)))), ((int)(((byte)(30)))));
            this.panelBarraSuperior.Controls.Add(this.lblTituloApp);
            this.panelBarraSuperior.Controls.Add(this.btnCerrar);
            this.panelBarraSuperior.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelBarraSuperior.Location = new System.Drawing.Point(0, 0);
            this.panelBarraSuperior.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.panelBarraSuperior.Name = "panelBarraSuperior";
            this.panelBarraSuperior.Size = new System.Drawing.Size(1920, 94);
            this.panelBarraSuperior.TabIndex = 0;
            // 
            // lblTituloApp
            // 
            this.lblTituloApp.AutoSize = true;
            this.lblTituloApp.Font = new System.Drawing.Font("Segoe UI", 18F, System.Drawing.FontStyle.Bold);
            this.lblTituloApp.ForeColor = System.Drawing.Color.White;
            this.lblTituloApp.Location = new System.Drawing.Point(25, 23);
            this.lblTituloApp.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblTituloApp.Name = "lblTituloApp";
            this.lblTituloApp.Size = new System.Drawing.Size(550, 48);
            this.lblTituloApp.TabIndex = 0;
            this.lblTituloApp.Text = "Control de Producción ENAGAS";
            // 
            // btnCerrar
            // 
            this.btnCerrar.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCerrar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(209)))), ((int)(((byte)(17)))), ((int)(((byte)(65)))));
            this.btnCerrar.FlatAppearance.BorderSize = 0;
            this.btnCerrar.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCerrar.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnCerrar.ForeColor = System.Drawing.Color.White;
            this.btnCerrar.Location = new System.Drawing.Point(1858, 23);
            this.btnCerrar.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnCerrar.Name = "btnCerrar";
            this.btnCerrar.Size = new System.Drawing.Size(50, 47);
            this.btnCerrar.TabIndex = 1;
            this.btnCerrar.Text = "✕";
            this.btnCerrar.UseVisualStyleBackColor = false;
            this.btnCerrar.Click += new System.EventHandler(this.btnCerrar_Click);
            // 
            // panelEstados
            // 
            this.panelEstados.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.panelEstados.Controls.Add(this.panelEstadoMqtt);
            this.panelEstados.Controls.Add(this.lblEstadoMqtt);
            this.panelEstados.Controls.Add(this.panelEstadoCorreo);
            this.panelEstados.Controls.Add(this.lblEstadoCorreo);
            this.panelEstados.Controls.Add(this.panelEstadoModbus);
            this.panelEstados.Controls.Add(this.lblEstadoModbus);
            this.panelEstados.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelEstados.Location = new System.Drawing.Point(0, 94);
            this.panelEstados.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.panelEstados.Name = "panelEstados";
            this.panelEstados.Size = new System.Drawing.Size(1920, 78);
            this.panelEstados.TabIndex = 1;
            // 
            // panelEstadoMqtt
            // 
            this.panelEstadoMqtt.BackColor = System.Drawing.Color.Gray;
            this.panelEstadoMqtt.Location = new System.Drawing.Point(62, 23);
            this.panelEstadoMqtt.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.panelEstadoMqtt.Name = "panelEstadoMqtt";
            this.panelEstadoMqtt.Size = new System.Drawing.Size(25, 31);
            this.panelEstadoMqtt.TabIndex = 0;
            // 
            // lblEstadoMqtt
            // 
            this.lblEstadoMqtt.AutoSize = true;
            this.lblEstadoMqtt.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.lblEstadoMqtt.ForeColor = System.Drawing.Color.White;
            this.lblEstadoMqtt.Location = new System.Drawing.Point(100, 27);
            this.lblEstadoMqtt.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblEstadoMqtt.Name = "lblEstadoMqtt";
            this.lblEstadoMqtt.Size = new System.Drawing.Size(197, 28);
            this.lblEstadoMqtt.TabIndex = 1;
            this.lblEstadoMqtt.Text = "MQTT: Desconectado";
            // 
            // panelEstadoCorreo
            // 
            this.panelEstadoCorreo.BackColor = System.Drawing.Color.Gray;
            this.panelEstadoCorreo.Location = new System.Drawing.Point(312, 23);
            this.panelEstadoCorreo.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.panelEstadoCorreo.Name = "panelEstadoCorreo";
            this.panelEstadoCorreo.Size = new System.Drawing.Size(25, 31);
            this.panelEstadoCorreo.TabIndex = 2;
            // 
            // lblEstadoCorreo
            // 
            this.lblEstadoCorreo.AutoSize = true;
            this.lblEstadoCorreo.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.lblEstadoCorreo.ForeColor = System.Drawing.Color.White;
            this.lblEstadoCorreo.Location = new System.Drawing.Point(350, 27);
            this.lblEstadoCorreo.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblEstadoCorreo.Name = "lblEstadoCorreo";
            this.lblEstadoCorreo.Size = new System.Drawing.Size(205, 28);
            this.lblEstadoCorreo.TabIndex = 3;
            this.lblEstadoCorreo.Text = "Correo: Desconectado";
            // 
            // panelEstadoModbus
            // 
            this.panelEstadoModbus.BackColor = System.Drawing.Color.Gray;
            this.panelEstadoModbus.Location = new System.Drawing.Point(562, 23);
            this.panelEstadoModbus.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.panelEstadoModbus.Name = "panelEstadoModbus";
            this.panelEstadoModbus.Size = new System.Drawing.Size(25, 31);
            this.panelEstadoModbus.TabIndex = 4;
            // 
            // lblEstadoModbus
            // 
            this.lblEstadoModbus.AutoSize = true;
            this.lblEstadoModbus.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.lblEstadoModbus.ForeColor = System.Drawing.Color.White;
            this.lblEstadoModbus.Location = new System.Drawing.Point(600, 27);
            this.lblEstadoModbus.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblEstadoModbus.Name = "lblEstadoModbus";
            this.lblEstadoModbus.Size = new System.Drawing.Size(218, 28);
            this.lblEstadoModbus.TabIndex = 5;
            this.lblEstadoModbus.Text = "Modbus: Desconectado";
            // 
            // panelNavegacion
            // 
            this.panelNavegacion.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.panelNavegacion.Controls.Add(this.btnNavCorreo);
            this.panelNavegacion.Controls.Add(this.btnNavMqtt);
            this.panelNavegacion.Controls.Add(this.btnNavModbus);
            this.panelNavegacion.Controls.Add(this.btnNavSinoptico);
            this.panelNavegacion.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelNavegacion.Location = new System.Drawing.Point(0, 172);
            this.panelNavegacion.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.panelNavegacion.Name = "panelNavegacion";
            this.panelNavegacion.Size = new System.Drawing.Size(1920, 94);
            this.panelNavegacion.TabIndex = 2;
            // 
            // btnNavCorreo
            // 
            this.btnNavCorreo.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnNavCorreo.FlatAppearance.BorderSize = 0;
            this.btnNavCorreo.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNavCorreo.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnNavCorreo.ForeColor = System.Drawing.Color.White;
            this.btnNavCorreo.Location = new System.Drawing.Point(62, 23);
            this.btnNavCorreo.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnNavCorreo.Name = "btnNavCorreo";
            this.btnNavCorreo.Size = new System.Drawing.Size(250, 47);
            this.btnNavCorreo.TabIndex = 0;
            this.btnNavCorreo.Text = "📧 CORREO";
            this.btnNavCorreo.UseVisualStyleBackColor = false;
            this.btnNavCorreo.Click += new System.EventHandler(this.btnNavCorreo_Click);
            // 
            // btnNavMqtt
            // 
            this.btnNavMqtt.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.btnNavMqtt.FlatAppearance.BorderSize = 0;
            this.btnNavMqtt.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNavMqtt.Font = new System.Drawing.Font("Segoe UI", 12F);
            this.btnNavMqtt.ForeColor = System.Drawing.Color.White;
            this.btnNavMqtt.Location = new System.Drawing.Point(338, 23);
            this.btnNavMqtt.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnNavMqtt.Name = "btnNavMqtt";
            this.btnNavMqtt.Size = new System.Drawing.Size(250, 47);
            this.btnNavMqtt.TabIndex = 1;
            this.btnNavMqtt.Text = "🌐 MQTT";
            this.btnNavMqtt.UseVisualStyleBackColor = false;
            this.btnNavMqtt.Click += new System.EventHandler(this.btnNavMqtt_Click);
            // 
            // btnNavModbus
            // 
            this.btnNavModbus.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.btnNavModbus.FlatAppearance.BorderSize = 0;
            this.btnNavModbus.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNavModbus.Font = new System.Drawing.Font("Segoe UI", 12F);
            this.btnNavModbus.ForeColor = System.Drawing.Color.White;
            this.btnNavModbus.Location = new System.Drawing.Point(612, 23);
            this.btnNavModbus.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnNavModbus.Name = "btnNavModbus";
            this.btnNavModbus.Size = new System.Drawing.Size(250, 47);
            this.btnNavModbus.TabIndex = 2;
            this.btnNavModbus.Text = "⚙️ MODBUS";
            this.btnNavModbus.UseVisualStyleBackColor = false;
            this.btnNavModbus.Click += new System.EventHandler(this.btnNavModbus_Click);
            // 
            // btnNavSinoptico
            // 
            this.btnNavSinoptico.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.btnNavSinoptico.FlatAppearance.BorderSize = 0;
            this.btnNavSinoptico.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNavSinoptico.Font = new System.Drawing.Font("Segoe UI", 12F);
            this.btnNavSinoptico.ForeColor = System.Drawing.Color.White;
            this.btnNavSinoptico.Location = new System.Drawing.Point(888, 23);
            this.btnNavSinoptico.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnNavSinoptico.Name = "btnNavSinoptico";
            this.btnNavSinoptico.Size = new System.Drawing.Size(250, 47);
            this.btnNavSinoptico.TabIndex = 3;
            this.btnNavSinoptico.Text = "📊 SINÓPTICO";
            this.btnNavSinoptico.UseVisualStyleBackColor = false;
            this.btnNavSinoptico.Click += new System.EventHandler(this.btnNavSinoptico_Click);
            // 
            // panelLog
            // 
            this.panelLog.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(40)))), ((int)(((byte)(40)))), ((int)(((byte)(40)))));
            this.panelLog.Controls.Add(this.rtbLog);
            this.panelLog.Controls.Add(this.btnLimpiarLog);
            this.panelLog.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelLog.Location = new System.Drawing.Point(0, 894);
            this.panelLog.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.panelLog.Name = "panelLog";
            this.panelLog.Size = new System.Drawing.Size(1920, 312);
            this.panelLog.TabIndex = 4;
            // 
            // rtbLog
            // 
            this.rtbLog.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rtbLog.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(30)))), ((int)(((byte)(30)))));
            this.rtbLog.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.rtbLog.Font = new System.Drawing.Font("Consolas", 9F);
            this.rtbLog.ForeColor = System.Drawing.Color.LightGray;
            this.rtbLog.Location = new System.Drawing.Point(25, 31);
            this.rtbLog.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.rtbLog.Name = "rtbLog";
            this.rtbLog.ReadOnly = true;
            this.rtbLog.Size = new System.Drawing.Size(1762, 250);
            this.rtbLog.TabIndex = 0;
            this.rtbLog.Text = "=== LOG DEL SISTEMA ===\nSistema iniciado correctamente...\n";
            // 
            // btnLimpiarLog
            // 
            this.btnLimpiarLog.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnLimpiarLog.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(209)))), ((int)(((byte)(17)))), ((int)(((byte)(65)))));
            this.btnLimpiarLog.FlatAppearance.BorderSize = 0;
            this.btnLimpiarLog.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnLimpiarLog.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.btnLimpiarLog.ForeColor = System.Drawing.Color.White;
            this.btnLimpiarLog.Location = new System.Drawing.Point(1808, 31);
            this.btnLimpiarLog.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnLimpiarLog.Name = "btnLimpiarLog";
            this.btnLimpiarLog.Size = new System.Drawing.Size(100, 47);
            this.btnLimpiarLog.TabIndex = 1;
            this.btnLimpiarLog.Text = "Limpiar";
            this.btnLimpiarLog.UseVisualStyleBackColor = false;
            this.btnLimpiarLog.Click += new System.EventHandler(this.btnLimpiarLog_Click);
            // 
            // tabControlPrincipal
            // 
            this.tabControlPrincipal.Controls.Add(this.tabPageCorreo);
            this.tabControlPrincipal.Controls.Add(this.tabPageMqtt);
            this.tabControlPrincipal.Controls.Add(this.tabPageModbus);
            this.tabControlPrincipal.Controls.Add(this.tabPageSinoptico);
            this.tabControlPrincipal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlPrincipal.Location = new System.Drawing.Point(0, 266);
            this.tabControlPrincipal.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.tabControlPrincipal.Name = "tabControlPrincipal";
            this.tabControlPrincipal.SelectedIndex = 0;
            this.tabControlPrincipal.Size = new System.Drawing.Size(1920, 628);
            this.tabControlPrincipal.TabIndex = 5;
            // 
            // tabPageCorreo
            // 
            this.tabPageCorreo.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(48)))));
            this.tabPageCorreo.Controls.Add(this.grpConfigCorreo);
            this.tabPageCorreo.Location = new System.Drawing.Point(4, 34);
            this.tabPageCorreo.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.tabPageCorreo.Name = "tabPageCorreo";
            this.tabPageCorreo.Padding = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.tabPageCorreo.Size = new System.Drawing.Size(1912, 590);
            this.tabPageCorreo.TabIndex = 0;
            this.tabPageCorreo.Text = "Correo";
            // 
            // grpConfigCorreo
            // 
            this.grpConfigCorreo.Controls.Add(this.lblEwsServerUrl);
            this.grpConfigCorreo.Controls.Add(this.txtEwsServerUrl);
            this.grpConfigCorreo.Controls.Add(this.lblEwsUsuario);
            this.grpConfigCorreo.Controls.Add(this.txtEwsUsuario);
            this.grpConfigCorreo.Controls.Add(this.lblEwsPassword);
            this.grpConfigCorreo.Controls.Add(this.txtEwsPassword);
            this.grpConfigCorreo.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.grpConfigCorreo.ForeColor = System.Drawing.Color.White;
            this.grpConfigCorreo.Location = new System.Drawing.Point(62, 47);
            this.grpConfigCorreo.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.grpConfigCorreo.Name = "grpConfigCorreo";
            this.grpConfigCorreo.Padding = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.grpConfigCorreo.Size = new System.Drawing.Size(1000, 469);
            this.grpConfigCorreo.TabIndex = 0;
            this.grpConfigCorreo.TabStop = false;
            this.grpConfigCorreo.Text = "Configuración Exchange (EWS)";
            // 
            // lblEwsServerUrl
            // 
            this.lblEwsServerUrl.AutoSize = true;
            this.lblEwsServerUrl.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.lblEwsServerUrl.ForeColor = System.Drawing.Color.White;
            this.lblEwsServerUrl.Location = new System.Drawing.Point(38, 78);
            this.lblEwsServerUrl.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblEwsServerUrl.Name = "lblEwsServerUrl";
            this.lblEwsServerUrl.Size = new System.Drawing.Size(180, 30);
            this.lblEwsServerUrl.TabIndex = 0;
            this.lblEwsServerUrl.Text = "URL del Servidor:";
            // 
            // txtEwsServerUrl
            // 
            this.txtEwsServerUrl.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.txtEwsServerUrl.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtEwsServerUrl.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.txtEwsServerUrl.ForeColor = System.Drawing.Color.White;
            this.txtEwsServerUrl.Location = new System.Drawing.Point(250, 75);
            this.txtEwsServerUrl.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtEwsServerUrl.Name = "txtEwsServerUrl";
            this.txtEwsServerUrl.Size = new System.Drawing.Size(687, 37);
            this.txtEwsServerUrl.TabIndex = 1;
            this.txtEwsServerUrl.Text = "https://outlook.office365.com/EWS/Exchange.asmx";
            // 
            // lblEwsUsuario
            // 
            this.lblEwsUsuario.AutoSize = true;
            this.lblEwsUsuario.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.lblEwsUsuario.ForeColor = System.Drawing.Color.White;
            this.lblEwsUsuario.Location = new System.Drawing.Point(38, 156);
            this.lblEwsUsuario.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblEwsUsuario.Name = "lblEwsUsuario";
            this.lblEwsUsuario.Size = new System.Drawing.Size(91, 30);
            this.lblEwsUsuario.TabIndex = 2;
            this.lblEwsUsuario.Text = "Usuario:";
            // 
            // txtEwsUsuario
            // 
            this.txtEwsUsuario.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.txtEwsUsuario.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtEwsUsuario.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.txtEwsUsuario.ForeColor = System.Drawing.Color.White;
            this.txtEwsUsuario.Location = new System.Drawing.Point(250, 153);
            this.txtEwsUsuario.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtEwsUsuario.Name = "txtEwsUsuario";
            this.txtEwsUsuario.Size = new System.Drawing.Size(500, 37);
            this.txtEwsUsuario.TabIndex = 3;
            // 
            // lblEwsPassword
            // 
            this.lblEwsPassword.AutoSize = true;
            this.lblEwsPassword.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.lblEwsPassword.ForeColor = System.Drawing.Color.White;
            this.lblEwsPassword.Location = new System.Drawing.Point(38, 234);
            this.lblEwsPassword.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblEwsPassword.Name = "lblEwsPassword";
            this.lblEwsPassword.Size = new System.Drawing.Size(127, 30);
            this.lblEwsPassword.TabIndex = 4;
            this.lblEwsPassword.Text = "Contraseña:";
            // 
            // txtEwsPassword
            // 
            this.txtEwsPassword.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.txtEwsPassword.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtEwsPassword.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.txtEwsPassword.ForeColor = System.Drawing.Color.White;
            this.txtEwsPassword.Location = new System.Drawing.Point(250, 231);
            this.txtEwsPassword.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtEwsPassword.Name = "txtEwsPassword";
            this.txtEwsPassword.PasswordChar = '*';
            this.txtEwsPassword.Size = new System.Drawing.Size(500, 37);
            this.txtEwsPassword.TabIndex = 5;
            // 
            // tabPageMqtt
            // 
            this.tabPageMqtt.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(48)))));
            this.tabPageMqtt.Controls.Add(this.grpConexionMqtt);
            this.tabPageMqtt.Location = new System.Drawing.Point(4, 34);
            this.tabPageMqtt.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.tabPageMqtt.Name = "tabPageMqtt";
            this.tabPageMqtt.Padding = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.tabPageMqtt.Size = new System.Drawing.Size(1912, 590);
            this.tabPageMqtt.TabIndex = 1;
            this.tabPageMqtt.Text = "MQTT";
            // 
            // grpConexionMqtt
            // 
            this.grpConexionMqtt.Controls.Add(this.lblMqttProtocolo);
            this.grpConexionMqtt.Controls.Add(this.cmbMqttProtocolo);
            this.grpConexionMqtt.Controls.Add(this.lblMqttHost);
            this.grpConexionMqtt.Controls.Add(this.txtMqttHost);
            this.grpConexionMqtt.Controls.Add(this.lblMqttPuerto);
            this.grpConexionMqtt.Controls.Add(this.txtMqttPuerto);
            this.grpConexionMqtt.Controls.Add(this.lblMqttClientId);
            this.grpConexionMqtt.Controls.Add(this.txtMqttClientId);
            this.grpConexionMqtt.Controls.Add(this.lblMqttUsuario);
            this.grpConexionMqtt.Controls.Add(this.txtMqttUsuario);
            this.grpConexionMqtt.Controls.Add(this.lblMqttPassword);
            this.grpConexionMqtt.Controls.Add(this.txtMqttPassword);
            this.grpConexionMqtt.Controls.Add(this.chkMqttSslTls);
            this.grpConexionMqtt.Controls.Add(this.btnMqttConectar);
            this.grpConexionMqtt.Controls.Add(this.btnMqttDesconectar);
            this.grpConexionMqtt.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.grpConexionMqtt.ForeColor = System.Drawing.Color.White;
            this.grpConexionMqtt.Location = new System.Drawing.Point(62, 47);
            this.grpConexionMqtt.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.grpConexionMqtt.Name = "grpConexionMqtt";
            this.grpConexionMqtt.Padding = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.grpConexionMqtt.Size = new System.Drawing.Size(1000, 500);
            this.grpConexionMqtt.TabIndex = 1;
            this.grpConexionMqtt.TabStop = false;
            this.grpConexionMqtt.Text = "Conexión MQTT";
            // 
            // lblMqttProtocolo
            // 
            this.lblMqttProtocolo.AutoSize = true;
            this.lblMqttProtocolo.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.lblMqttProtocolo.ForeColor = System.Drawing.Color.White;
            this.lblMqttProtocolo.Location = new System.Drawing.Point(38, 78);
            this.lblMqttProtocolo.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblMqttProtocolo.Name = "lblMqttProtocolo";
            this.lblMqttProtocolo.Size = new System.Drawing.Size(112, 30);
            this.lblMqttProtocolo.TabIndex = 0;
            this.lblMqttProtocolo.Text = "Protocolo:";
            // 
            // cmbMqttProtocolo
            // 
            this.cmbMqttProtocolo.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.cmbMqttProtocolo.ForeColor = System.Drawing.Color.White;
            this.cmbMqttProtocolo.FormattingEnabled = true;
            this.cmbMqttProtocolo.Items.AddRange(new object[] {
            "mqtt://",
            "mqtts://",
            "ws://",
            "wss://"});
            this.cmbMqttProtocolo.Location = new System.Drawing.Point(188, 75);
            this.cmbMqttProtocolo.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbMqttProtocolo.Name = "cmbMqttProtocolo";
            this.cmbMqttProtocolo.Size = new System.Drawing.Size(149, 40);
            this.cmbMqttProtocolo.TabIndex = 1;
            // 
            // lblMqttHost
            // 
            this.lblMqttHost.AutoSize = true;
            this.lblMqttHost.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.lblMqttHost.Location = new System.Drawing.Point(360, 78);
            this.lblMqttHost.Name = "lblMqttHost";
            this.lblMqttHost.Size = new System.Drawing.Size(61, 30);
            this.lblMqttHost.TabIndex = 2;
            this.lblMqttHost.Text = "Host:";
            // 
            // txtMqttHost
            // 
            this.txtMqttHost.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.txtMqttHost.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtMqttHost.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.txtMqttHost.ForeColor = System.Drawing.Color.White;
            this.txtMqttHost.Location = new System.Drawing.Point(430, 75);
            this.txtMqttHost.Name = "txtMqttHost";
            this.txtMqttHost.Size = new System.Drawing.Size(250, 37);
            this.txtMqttHost.TabIndex = 3;
            // 
            // lblMqttPuerto
            // 
            this.lblMqttPuerto.AutoSize = true;
            this.lblMqttPuerto.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.lblMqttPuerto.Location = new System.Drawing.Point(700, 78);
            this.lblMqttPuerto.Name = "lblMqttPuerto";
            this.lblMqttPuerto.Size = new System.Drawing.Size(78, 30);
            this.lblMqttPuerto.TabIndex = 4;
            this.lblMqttPuerto.Text = "Puerto:";
            // 
            // txtMqttPuerto
            // 
            this.txtMqttPuerto.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.txtMqttPuerto.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtMqttPuerto.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.txtMqttPuerto.ForeColor = System.Drawing.Color.White;
            this.txtMqttPuerto.Location = new System.Drawing.Point(790, 75);
            this.txtMqttPuerto.Name = "txtMqttPuerto";
            this.txtMqttPuerto.Size = new System.Drawing.Size(100, 37);
            this.txtMqttPuerto.TabIndex = 5;
            // 
            // lblMqttClientId
            // 
            this.lblMqttClientId.AutoSize = true;
            this.lblMqttClientId.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.lblMqttClientId.Location = new System.Drawing.Point(38, 140);
            this.lblMqttClientId.Name = "lblMqttClientId";
            this.lblMqttClientId.Size = new System.Drawing.Size(101, 30);
            this.lblMqttClientId.TabIndex = 6;
            this.lblMqttClientId.Text = "Client ID:";
            // 
            // txtMqttClientId
            // 
            this.txtMqttClientId.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.txtMqttClientId.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtMqttClientId.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.txtMqttClientId.ForeColor = System.Drawing.Color.White;
            this.txtMqttClientId.Location = new System.Drawing.Point(188, 138);
            this.txtMqttClientId.Name = "txtMqttClientId";
            this.txtMqttClientId.Size = new System.Drawing.Size(400, 37);
            this.txtMqttClientId.TabIndex = 7;
            // 
            // lblMqttUsuario
            // 
            this.lblMqttUsuario.AutoSize = true;
            this.lblMqttUsuario.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.lblMqttUsuario.Location = new System.Drawing.Point(38, 200);
            this.lblMqttUsuario.Name = "lblMqttUsuario";
            this.lblMqttUsuario.Size = new System.Drawing.Size(91, 30);
            this.lblMqttUsuario.TabIndex = 8;
            this.lblMqttUsuario.Text = "Usuario:";
            // 
            // txtMqttUsuario
            // 
            this.txtMqttUsuario.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.txtMqttUsuario.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtMqttUsuario.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.txtMqttUsuario.ForeColor = System.Drawing.Color.White;
            this.txtMqttUsuario.Location = new System.Drawing.Point(188, 198);
            this.txtMqttUsuario.Name = "txtMqttUsuario";
            this.txtMqttUsuario.Size = new System.Drawing.Size(400, 37);
            this.txtMqttUsuario.TabIndex = 9;
            // 
            // lblMqttPassword
            // 
            this.lblMqttPassword.AutoSize = true;
            this.lblMqttPassword.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.lblMqttPassword.Location = new System.Drawing.Point(38, 260);
            this.lblMqttPassword.Name = "lblMqttPassword";
            this.lblMqttPassword.Size = new System.Drawing.Size(127, 30);
            this.lblMqttPassword.TabIndex = 10;
            this.lblMqttPassword.Text = "Contraseña:";
            // 
            // txtMqttPassword
            // 
            this.txtMqttPassword.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.txtMqttPassword.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtMqttPassword.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.txtMqttPassword.ForeColor = System.Drawing.Color.White;
            this.txtMqttPassword.Location = new System.Drawing.Point(188, 258);
            this.txtMqttPassword.Name = "txtMqttPassword";
            this.txtMqttPassword.PasswordChar = '*';
            this.txtMqttPassword.Size = new System.Drawing.Size(400, 37);
            this.txtMqttPassword.TabIndex = 11;
            // 
            // chkMqttSslTls
            // 
            this.chkMqttSslTls.AutoSize = true;
            this.chkMqttSslTls.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.chkMqttSslTls.Location = new System.Drawing.Point(42, 330);
            this.chkMqttSslTls.Name = "chkMqttSslTls";
            this.chkMqttSslTls.Size = new System.Drawing.Size(189, 34);
            this.chkMqttSslTls.TabIndex = 12;
            this.chkMqttSslTls.Text = "Habilitar SSL/TLS";
            this.chkMqttSslTls.UseVisualStyleBackColor = true;
            // 
            // btnMqttConectar
            // 
            this.btnMqttConectar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnMqttConectar.FlatAppearance.BorderSize = 0;
            this.btnMqttConectar.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnMqttConectar.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold);
            this.btnMqttConectar.ForeColor = System.Drawing.Color.White;
            this.btnMqttConectar.Location = new System.Drawing.Point(42, 400);
            this.btnMqttConectar.Name = "btnMqttConectar";
            this.btnMqttConectar.Size = new System.Drawing.Size(150, 47);
            this.btnMqttConectar.TabIndex = 13;
            this.btnMqttConectar.Text = "Conectar";
            this.btnMqttConectar.UseVisualStyleBackColor = false;
            // 
            // btnMqttDesconectar
            // 
            this.btnMqttDesconectar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(209)))), ((int)(((byte)(17)))), ((int)(((byte)(65)))));
            this.btnMqttDesconectar.FlatAppearance.BorderSize = 0;
            this.btnMqttDesconectar.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnMqttDesconectar.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold);
            this.btnMqttDesconectar.ForeColor = System.Drawing.Color.White;
            this.btnMqttDesconectar.Location = new System.Drawing.Point(212, 400);
            this.btnMqttDesconectar.Name = "btnMqttDesconectar";
            this.btnMqttDesconectar.Size = new System.Drawing.Size(150, 47);
            this.btnMqttDesconectar.TabIndex = 14;
            this.btnMqttDesconectar.Text = "Desconectar";
            this.btnMqttDesconectar.UseVisualStyleBackColor = false;
            // 
            // tabPageModbus
            // 
            this.tabPageModbus.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(48)))));
            this.tabPageModbus.Controls.Add(this.grpOperacionesModbus);
            this.tabPageModbus.Controls.Add(this.grpConexionModbus);
            this.tabPageModbus.Controls.Add(this.dgvModbusResultados);
            this.tabPageModbus.Controls.Add(this.grpConfigModbus);
            this.tabPageModbus.Location = new System.Drawing.Point(4, 34);
            this.tabPageModbus.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.tabPageModbus.Name = "tabPageModbus";
            this.tabPageModbus.Size = new System.Drawing.Size(1912, 590);
            this.tabPageModbus.TabIndex = 2;
            this.tabPageModbus.Text = "Modbus";
            // 
            // grpOperacionesModbus
            // 
            this.grpOperacionesModbus.Controls.Add(this.lblModbusDireccionRegistro);
            this.grpOperacionesModbus.Controls.Add(this.txtModbusDireccionRegistro);
            this.grpOperacionesModbus.Controls.Add(this.lblModbusValorEscritura);
            this.grpOperacionesModbus.Controls.Add(this.txtModbusValorEscritura);
            this.grpOperacionesModbus.Controls.Add(this.btnModbusLeerRegistros);
            this.grpOperacionesModbus.Controls.Add(this.btnModbusEscribirRegistro);
            this.grpOperacionesModbus.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.grpOperacionesModbus.ForeColor = System.Drawing.Color.White;
            this.grpOperacionesModbus.Location = new System.Drawing.Point(62, 260);
            this.grpOperacionesModbus.Name = "grpOperacionesModbus";
            this.grpOperacionesModbus.Size = new System.Drawing.Size(550, 180);
            this.grpOperacionesModbus.TabIndex = 3;
            this.grpOperacionesModbus.TabStop = false;
            this.grpOperacionesModbus.Text = "Operaciones";
            // 
            // lblModbusDireccionRegistro
            // 
            this.lblModbusDireccionRegistro.AutoSize = true;
            this.lblModbusDireccionRegistro.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.lblModbusDireccionRegistro.Location = new System.Drawing.Point(20, 50);
            this.lblModbusDireccionRegistro.Name = "lblModbusDireccionRegistro";
            this.lblModbusDireccionRegistro.Size = new System.Drawing.Size(107, 30);
            this.lblModbusDireccionRegistro.TabIndex = 0;
            this.lblModbusDireccionRegistro.Text = "Dirección:";
            // 
            // txtModbusDireccionRegistro
            // 
            this.txtModbusDireccionRegistro.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.txtModbusDireccionRegistro.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtModbusDireccionRegistro.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.txtModbusDireccionRegistro.ForeColor = System.Drawing.Color.White;
            this.txtModbusDireccionRegistro.Location = new System.Drawing.Point(140, 48);
            this.txtModbusDireccionRegistro.Name = "txtModbusDireccionRegistro";
            this.txtModbusDireccionRegistro.Size = new System.Drawing.Size(120, 37);
            this.txtModbusDireccionRegistro.TabIndex = 1;
            // 
            // lblModbusValorEscritura
            // 
            this.lblModbusValorEscritura.AutoSize = true;
            this.lblModbusValorEscritura.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.lblModbusValorEscritura.Location = new System.Drawing.Point(280, 50);
            this.lblModbusValorEscritura.Name = "lblModbusValorEscritura";
            this.lblModbusValorEscritura.Size = new System.Drawing.Size(66, 30);
            this.lblModbusValorEscritura.TabIndex = 2;
            this.lblModbusValorEscritura.Text = "Valor:";
            // 
            // txtModbusValorEscritura
            // 
            this.txtModbusValorEscritura.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.txtModbusValorEscritura.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtModbusValorEscritura.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.txtModbusValorEscritura.ForeColor = System.Drawing.Color.White;
            this.txtModbusValorEscritura.Location = new System.Drawing.Point(360, 48);
            this.txtModbusValorEscritura.Name = "txtModbusValorEscritura";
            this.txtModbusValorEscritura.Size = new System.Drawing.Size(120, 37);
            this.txtModbusValorEscritura.TabIndex = 3;
            // 
            // btnModbusLeerRegistros
            // 
            this.btnModbusLeerRegistros.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnModbusLeerRegistros.FlatAppearance.BorderSize = 0;
            this.btnModbusLeerRegistros.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnModbusLeerRegistros.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            this.btnModbusLeerRegistros.Location = new System.Drawing.Point(24, 110);
            this.btnModbusLeerRegistros.Name = "btnModbusLeerRegistros";
            this.btnModbusLeerRegistros.Size = new System.Drawing.Size(200, 47);
            this.btnModbusLeerRegistros.TabIndex = 4;
            this.btnModbusLeerRegistros.Text = "Leer Registros";
            this.btnModbusLeerRegistros.UseVisualStyleBackColor = false;
            // 
            // btnModbusEscribirRegistro
            // 
            this.btnModbusEscribirRegistro.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(209)))), ((int)(((byte)(17)))), ((int)(((byte)(65)))));
            this.btnModbusEscribirRegistro.FlatAppearance.BorderSize = 0;
            this.btnModbusEscribirRegistro.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnModbusEscribirRegistro.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            this.btnModbusEscribirRegistro.Location = new System.Drawing.Point(240, 110);
            this.btnModbusEscribirRegistro.Name = "btnModbusEscribirRegistro";
            this.btnModbusEscribirRegistro.Size = new System.Drawing.Size(200, 47);
            this.btnModbusEscribirRegistro.TabIndex = 5;
            this.btnModbusEscribirRegistro.Text = "Escribir Registro";
            this.btnModbusEscribirRegistro.UseVisualStyleBackColor = false;
            // 
            // grpConexionModbus
            // 
            this.grpConexionModbus.Controls.Add(this.lblModbusIp);
            this.grpConexionModbus.Controls.Add(this.txtModbusIp);
            this.grpConexionModbus.Controls.Add(this.lblModbusPuerto);
            this.grpConexionModbus.Controls.Add(this.nudModbusPuerto);
            this.grpConexionModbus.Controls.Add(this.lblModbusDeviceId);
            this.grpConexionModbus.Controls.Add(this.nudModbusDeviceId);
            this.grpConexionModbus.Controls.Add(this.btnModbusConectar);
            this.grpConexionModbus.Controls.Add(this.btnModbusDesconectar);
            this.grpConexionModbus.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.grpConexionModbus.ForeColor = System.Drawing.Color.White;
            this.grpConexionModbus.Location = new System.Drawing.Point(628, 47);
            this.grpConexionModbus.Name = "grpConexionModbus";
            this.grpConexionModbus.Size = new System.Drawing.Size(434, 300);
            this.grpConexionModbus.TabIndex = 2;
            this.grpConexionModbus.TabStop = false;
            this.grpConexionModbus.Text = "Conexión TCP";
            // 
            // lblModbusIp
            // 
            this.lblModbusIp.AutoSize = true;
            this.lblModbusIp.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.lblModbusIp.Location = new System.Drawing.Point(20, 50);
            this.lblModbusIp.Name = "lblModbusIp";
            this.lblModbusIp.Size = new System.Drawing.Size(99, 30);
            this.lblModbusIp.TabIndex = 0;
            this.lblModbusIp.Text = "Dirección IP:";
            // 
            // txtModbusIp
            // 
            this.txtModbusIp.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.txtModbusIp.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtModbusIp.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.txtModbusIp.ForeColor = System.Drawing.Color.White;
            this.txtModbusIp.Location = new System.Drawing.Point(170, 48);
            this.txtModbusIp.Name = "txtModbusIp";
            this.txtModbusIp.Size = new System.Drawing.Size(200, 37);
            this.txtModbusIp.TabIndex = 1;
            // 
            // lblModbusPuerto
            // 
            this.lblModbusPuerto.AutoSize = true;
            this.lblModbusPuerto.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.lblModbusPuerto.Location = new System.Drawing.Point(20, 100);
            this.lblModbusPuerto.Name = "lblModbusPuerto";
            this.lblModbusPuerto.Size = new System.Drawing.Size(78, 30);
            this.lblModbusPuerto.TabIndex = 2;
            this.lblModbusPuerto.Text = "Puerto:";
            // 
            // nudModbusPuerto
            // 
            this.nudModbusPuerto.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.nudModbusPuerto.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.nudModbusPuerto.ForeColor = System.Drawing.Color.White;
            this.nudModbusPuerto.Location = new System.Drawing.Point(170, 98);
            this.nudModbusPuerto.Maximum = new decimal(new int[] {
            65535,
            0,
            0,
            0});
            this.nudModbusPuerto.Name = "nudModbusPuerto";
            this.nudModbusPuerto.Size = new System.Drawing.Size(120, 37);
            this.nudModbusPuerto.TabIndex = 3;
            // 
            // lblModbusDeviceId
            // 
            this.lblModbusDeviceId.AutoSize = true;
            this.lblModbusDeviceId.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.lblModbusDeviceId.Location = new System.Drawing.Point(20, 150);
            this.lblModbusDeviceId.Name = "lblModbusDeviceId";
            this.lblModbusDeviceId.Size = new System.Drawing.Size(145, 30);
            this.lblModbusDeviceId.TabIndex = 4;
            this.lblModbusDeviceId.Text = "ID Dispositivo:";
            // 
            // nudModbusDeviceId
            // 
            this.nudModbusDeviceId.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.nudModbusDeviceId.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.nudModbusDeviceId.ForeColor = System.Drawing.Color.White;
            this.nudModbusDeviceId.Location = new System.Drawing.Point(170, 148);
            this.nudModbusDeviceId.Maximum = new decimal(new int[] {
            255,
            0,
            0,
            0});
            this.nudModbusDeviceId.Name = "nudModbusDeviceId";
            this.nudModbusDeviceId.Size = new System.Drawing.Size(120, 37);
            this.nudModbusDeviceId.TabIndex = 5;
            // 
            // btnModbusConectar
            // 
            this.btnModbusConectar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnModbusConectar.FlatAppearance.BorderSize = 0;
            this.btnModbusConectar.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnModbusConectar.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold);
            this.btnModbusConectar.Location = new System.Drawing.Point(24, 220);
            this.btnModbusConectar.Name = "btnModbusConectar";
            this.btnModbusConectar.Size = new System.Drawing.Size(150, 47);
            this.btnModbusConectar.TabIndex = 6;
            this.btnModbusConectar.Text = "Conectar";
            this.btnModbusConectar.UseVisualStyleBackColor = false;
            // 
            // btnModbusDesconectar
            // 
            this.btnModbusDesconectar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(209)))), ((int)(((byte)(17)))), ((int)(((byte)(65)))));
            this.btnModbusDesconectar.FlatAppearance.BorderSize = 0;
            this.btnModbusDesconectar.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnModbusDesconectar.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold);
            this.btnModbusDesconectar.Location = new System.Drawing.Point(190, 220);
            this.btnModbusDesconectar.Name = "btnModbusDesconectar";
            this.btnModbusDesconectar.Size = new System.Drawing.Size(150, 47);
            this.btnModbusDesconectar.TabIndex = 7;
            this.btnModbusDesconectar.Text = "Desconectar";
            this.btnModbusDesconectar.UseVisualStyleBackColor = false;
            // 
            // dgvModbusResultados
            // 
            this.dgvModbusResultados.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgvModbusResultados.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.dgvModbusResultados.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvModbusResultados.Location = new System.Drawing.Point(1088, 47);
            this.dgvModbusResultados.Name = "dgvModbusResultados";
            this.dgvModbusResultados.RowHeadersWidth = 62;
            this.dgvModbusResultados.RowTemplate.Height = 28;
            this.dgvModbusResultados.Size = new System.Drawing.Size(800, 500);
            this.dgvModbusResultados.TabIndex = 1;
            // 
            // grpConfigModbus
            // 
            this.grpConfigModbus.Controls.Add(this.cmbModbusConfig);
            this.grpConfigModbus.Controls.Add(this.txtModbusConfigNombre);
            this.grpConfigModbus.Controls.Add(this.btnModbusGuardarConfig);
            this.grpConfigModbus.Controls.Add(this.btnModbusNuevaConfig);
            this.grpConfigModbus.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.grpConfigModbus.ForeColor = System.Drawing.Color.White;
            this.grpConfigModbus.Location = new System.Drawing.Point(62, 47);
            this.grpConfigModbus.Name = "grpConfigModbus";
            this.grpConfigModbus.Size = new System.Drawing.Size(550, 180);
            this.grpConfigModbus.TabIndex = 0;
            this.grpConfigModbus.TabStop = false;
            this.grpConfigModbus.Text = "Configuración";
            // 
            // cmbModbusConfig
            // 
            this.cmbModbusConfig.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.cmbModbusConfig.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.cmbModbusConfig.ForeColor = System.Drawing.Color.White;
            this.cmbModbusConfig.FormattingEnabled = true;
            this.cmbModbusConfig.Location = new System.Drawing.Point(24, 50);
            this.cmbModbusConfig.Name = "cmbModbusConfig";
            this.cmbModbusConfig.Size = new System.Drawing.Size(250, 38);
            this.cmbModbusConfig.TabIndex = 0;
            // 
            // txtModbusConfigNombre
            // 
            this.txtModbusConfigNombre.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.txtModbusConfigNombre.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtModbusConfigNombre.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.txtModbusConfigNombre.ForeColor = System.Drawing.Color.White;
            this.txtModbusConfigNombre.Location = new System.Drawing.Point(290, 50);
            this.txtModbusConfigNombre.Name = "txtModbusConfigNombre";
            this.txtModbusConfigNombre.Size = new System.Drawing.Size(250, 37);
            this.txtModbusConfigNombre.TabIndex = 1;
            // 
            // btnModbusGuardarConfig
            // 
            this.btnModbusGuardarConfig.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnModbusGuardarConfig.FlatAppearance.BorderSize = 0;
            this.btnModbusGuardarConfig.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnModbusGuardarConfig.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            this.btnModbusGuardarConfig.Location = new System.Drawing.Point(24, 110);
            this.btnModbusGuardarConfig.Name = "btnModbusGuardarConfig";
            this.btnModbusGuardarConfig.Size = new System.Drawing.Size(120, 47);
            this.btnModbusGuardarConfig.TabIndex = 2;
            this.btnModbusGuardarConfig.Text = "Guardar";
            this.btnModbusGuardarConfig.UseVisualStyleBackColor = false;
            // 
            // btnModbusNuevaConfig
            // 
            this.btnModbusNuevaConfig.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.btnModbusNuevaConfig.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnModbusNuevaConfig.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            this.btnModbusNuevaConfig.Location = new System.Drawing.Point(160, 110);
            this.btnModbusNuevaConfig.Name = "btnModbusNuevaConfig";
            this.btnModbusNuevaConfig.Size = new System.Drawing.Size(120, 47);
            this.btnModbusNuevaConfig.TabIndex = 3;
            this.btnModbusNuevaConfig.Text = "Nuevo";
            this.btnModbusNuevaConfig.UseVisualStyleBackColor = false;
            // 
            // tabPageSinoptico
            // 
            this.tabPageSinoptico.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(48)))));
            this.tabPageSinoptico.Controls.Add(this.lblTopicosActivos);
            this.tabPageSinoptico.Controls.Add(this.lblUltimaActualizacion);
            this.tabPageSinoptico.Controls.Add(this.grpDatosSinoptico);
            this.tabPageSinoptico.Location = new System.Drawing.Point(4, 34);
            this.tabPageSinoptico.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.tabPageSinoptico.Name = "tabPageSinoptico";
            this.tabPageSinoptico.Size = new System.Drawing.Size(1912, 590);
            this.tabPageSinoptico.TabIndex = 3;
            this.tabPageSinoptico.Text = "Sinóptico";
            // 
            // lblTopicosActivos
            // 
            this.lblTopicosActivos.AutoSize = true;
            this.lblTopicosActivos.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.lblTopicosActivos.ForeColor = System.Drawing.Color.White;
            this.lblTopicosActivos.Location = new System.Drawing.Point(62, 580);
            this.lblTopicosActivos.Name = "lblTopicosActivos";
            this.lblTopicosActivos.Size = new System.Drawing.Size(155, 28);
            this.lblTopicosActivos.TabIndex = 2;
            this.lblTopicosActivos.Text = "Tópicos Activos: 0";
            // 
            // lblUltimaActualizacion
            // 
            this.lblUltimaActualizacion.AutoSize = true;
            this.lblUltimaActualizacion.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.lblUltimaActualizacion.ForeColor = System.Drawing.Color.White;
            this.lblUltimaActualizacion.Location = new System.Drawing.Point(300, 580);
            this.lblUltimaActualizacion.Name = "lblUltimaActualizacion";
            this.lblUltimaActualizacion.Size = new System.Drawing.Size(209, 28);
            this.lblUltimaActualizacion.TabIndex = 3;
            this.lblUltimaActualizacion.Text = "Última Actualización: --";
            // 
            // grpDatosSinoptico
            // 
            this.grpDatosSinoptico.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.grpDatosSinoptico.Controls.Add(this.txtSinopticoFiltro);
            this.grpDatosSinoptico.Controls.Add(this.btnSinopticoActualizar);
            this.grpDatosSinoptico.Controls.Add(this.dgvSinopticoDatos);
            this.grpDatosSinoptico.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.grpDatosSinoptico.ForeColor = System.Drawing.Color.White;
            this.grpDatosSinoptico.Location = new System.Drawing.Point(62, 47);
            this.grpDatosSinoptico.Name = "grpDatosSinoptico";
            this.grpDatosSinoptico.Size = new System.Drawing.Size(1826, 500);
            this.grpDatosSinoptico.TabIndex = 1;
            this.grpDatosSinoptico.TabStop = false;
            this.grpDatosSinoptico.Text = "Vista de Datos en Tiempo Real";
            // 
            // txtSinopticoFiltro
            // 
            this.txtSinopticoFiltro.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.txtSinopticoFiltro.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtSinopticoFiltro.Font = new System.Drawing.Font("Segoe UI", 11F);
            this.txtSinopticoFiltro.ForeColor = System.Drawing.Color.White;
            this.txtSinopticoFiltro.Location = new System.Drawing.Point(24, 50);
            this.txtSinopticoFiltro.Name = "txtSinopticoFiltro";
            this.txtSinopticoFiltro.Size = new System.Drawing.Size(400, 37);
            this.txtSinopticoFiltro.TabIndex = 0;
            // 
            // btnSinopticoActualizar
            // 
            this.btnSinopticoActualizar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnSinopticoActualizar.FlatAppearance.BorderSize = 0;
            this.btnSinopticoActualizar.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSinopticoActualizar.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            this.btnSinopticoActualizar.Location = new System.Drawing.Point(440, 48);
            this.btnSinopticoActualizar.Name = "btnSinopticoActualizar";
            this.btnSinopticoActualizar.Size = new System.Drawing.Size(150, 47);
            this.btnSinopticoActualizar.TabIndex = 1;
            this.btnSinopticoActualizar.Text = "Actualizar";
            this.btnSinopticoActualizar.UseVisualStyleBackColor = false;
            // 
            // dgvSinopticoDatos
            // 
            this.dgvSinopticoDatos.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgvSinopticoDatos.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(63)))), ((int)(((byte)(65)))));
            this.dgvSinopticoDatos.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvSinopticoDatos.Location = new System.Drawing.Point(24, 110);
            this.dgvSinopticoDatos.Name = "dgvSinopticoDatos";
            this.dgvSinopticoDatos.RowHeadersWidth = 62;
            this.dgvSinopticoDatos.RowTemplate.Height = 28;
            this.dgvSinopticoDatos.Size = new System.Drawing.Size(1778, 360);
            this.dgvSinopticoDatos.TabIndex = 2;
            // 
            // MAIN
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(10F, 25F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(48)))));
            this.ClientSize = new System.Drawing.Size(1920, 1206);
            this.Controls.Add(this.tabControlPrincipal);
            this.Controls.Add(this.panelNavegacion);
            this.Controls.Add(this.panelEstados);
            this.Controls.Add(this.panelBarraSuperior);
            this.Controls.Add(this.panelLog);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.Name = "MAIN";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Control de Producción ENAGAS";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.panelBarraSuperior.ResumeLayout(false);
            this.panelBarraSuperior.PerformLayout();
            this.panelEstados.ResumeLayout(false);
            this.panelEstados.PerformLayout();
            this.panelNavegacion.ResumeLayout(false);
            this.panelLog.ResumeLayout(false);
            this.tabControlPrincipal.ResumeLayout(false);
            this.tabPageCorreo.ResumeLayout(false);
            this.grpConfigCorreo.ResumeLayout(false);
            this.grpConfigCorreo.PerformLayout();
            this.tabPageMqtt.ResumeLayout(false);
            this.grpConexionMqtt.ResumeLayout(false);
            this.grpConexionMqtt.PerformLayout();
            this.tabPageModbus.ResumeLayout(false);
            this.grpOperacionesModbus.ResumeLayout(false);
            this.grpOperacionesModbus.PerformLayout();
            this.grpConexionModbus.ResumeLayout(false);
            this.grpConexionModbus.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudModbusPuerto)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudModbusDeviceId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvModbusResultados)).EndInit();
            this.grpConfigModbus.ResumeLayout(false);
            this.grpConfigModbus.PerformLayout();
            this.tabPageSinoptico.ResumeLayout(false);
            this.tabPageSinoptico.PerformLayout();
            this.grpDatosSinoptico.ResumeLayout(false);
            this.grpDatosSinoptico.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvSinopticoDatos)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panelBarraSuperior;
        private System.Windows.Forms.Label lblTituloApp;
        private System.Windows.Forms.Button btnCerrar;
        private System.Windows.Forms.Panel panelEstados;
        private System.Windows.Forms.Panel panelEstadoMqtt;
        private System.Windows.Forms.Label lblEstadoMqtt;
        private System.Windows.Forms.Panel panelEstadoCorreo;
        private System.Windows.Forms.Label lblEstadoCorreo;
        private System.Windows.Forms.Panel panelEstadoModbus;
        private System.Windows.Forms.Label lblEstadoModbus;
        private System.Windows.Forms.Panel panelNavegacion;
        private System.Windows.Forms.Button btnNavCorreo;
        private System.Windows.Forms.Button btnNavMqtt;
        private System.Windows.Forms.Button btnNavModbus;
        private System.Windows.Forms.Button btnNavSinoptico;
        private System.Windows.Forms.Panel panelLog;
        private System.Windows.Forms.RichTextBox rtbLog;
        private System.Windows.Forms.Button btnLimpiarLog;
        private System.Windows.Forms.TabControl tabControlPrincipal;
        private System.Windows.Forms.TabPage tabPageCorreo;
        private System.Windows.Forms.TabPage tabPageMqtt;
        private System.Windows.Forms.TabPage tabPageModbus;
        private System.Windows.Forms.TabPage tabPageSinoptico;
        private System.Windows.Forms.GroupBox grpConfigCorreo;
        private System.Windows.Forms.Label lblEwsServerUrl;
        private System.Windows.Forms.TextBox txtEwsServerUrl;
        private System.Windows.Forms.Label lblEwsUsuario;
        private System.Windows.Forms.TextBox txtEwsUsuario;
        private System.Windows.Forms.Label lblEwsPassword;
        private System.Windows.Forms.TextBox txtEwsPassword;
        private System.Windows.Forms.GroupBox grpConexionMqtt;
        private System.Windows.Forms.Label lblMqttProtocolo;
        private System.Windows.Forms.ComboBox cmbMqttProtocolo;
        private System.Windows.Forms.Label lblMqttHost;
        private System.Windows.Forms.TextBox txtMqttHost;
        private System.Windows.Forms.Label lblMqttPuerto;
        private System.Windows.Forms.TextBox txtMqttPuerto;
        private System.Windows.Forms.Label lblMqttClientId;
        private System.Windows.Forms.TextBox txtMqttClientId;
        private System.Windows.Forms.Label lblMqttUsuario;
        private System.Windows.Forms.TextBox txtMqttUsuario;
        private System.Windows.Forms.Label lblMqttPassword;
        private System.Windows.Forms.TextBox txtMqttPassword;
        private System.Windows.Forms.CheckBox chkMqttSslTls;
        private System.Windows.Forms.Button btnMqttConectar;
        private System.Windows.Forms.Button btnMqttDesconectar;
        private System.Windows.Forms.GroupBox grpConfigModbus;
        private System.Windows.Forms.ComboBox cmbModbusConfig;
        private System.Windows.Forms.TextBox txtModbusConfigNombre;
        private System.Windows.Forms.Button btnModbusGuardarConfig;
        private System.Windows.Forms.Button btnModbusNuevaConfig;
        private System.Windows.Forms.DataGridView dgvModbusResultados;
        private System.Windows.Forms.GroupBox grpConexionModbus;
        private System.Windows.Forms.Label lblModbusIp;
        private System.Windows.Forms.TextBox txtModbusIp;
        private System.Windows.Forms.Label lblModbusPuerto;
        private System.Windows.Forms.NumericUpDown nudModbusPuerto;
        private System.Windows.Forms.Label lblModbusDeviceId;
        private System.Windows.Forms.NumericUpDown nudModbusDeviceId;
        private System.Windows.Forms.Button btnModbusConectar;
        private System.Windows.Forms.Button btnModbusDesconectar;
        private System.Windows.Forms.GroupBox grpOperacionesModbus;
        private System.Windows.Forms.Label lblModbusDireccionRegistro;
        private System.Windows.Forms.TextBox txtModbusDireccionRegistro;
        private System.Windows.Forms.Label lblModbusValorEscritura;
        private System.Windows.Forms.TextBox txtModbusValorEscritura;
        private System.Windows.Forms.Button btnModbusLeerRegistros;
        private System.Windows.Forms.Button btnModbusEscribirRegistro;
        private System.Windows.Forms.GroupBox grpDatosSinoptico;
        private System.Windows.Forms.TextBox txtSinopticoFiltro;
        private System.Windows.Forms.Button btnSinopticoActualizar;
        private System.Windows.Forms.DataGridView dgvSinopticoDatos;
        private System.Windows.Forms.Label lblTopicosActivos;
        private System.Windows.Forms.Label lblUltimaActualizacion;
    }
}
