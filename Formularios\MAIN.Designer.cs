﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace ControlDeProducciónENAGAS.Formularios
{
    partial class MAIN
    {
        /// <summary>
        /// Variable del diseñador necesaria.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Limpiar los recursos que se estén usando.
        /// </summary>
        /// <param name="disposing">true si los recursos administrados se deben desechar; false en caso contrario.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Código generado por el Diseñador de Windows Forms

        /// <summary>
        /// Método necesario para admitir el Diseñador. No se puede modificar
        /// el contenido de este método con el editor de código.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            panelBarraSuperior = new Panel();
            lblTituloApp = new Label();
            btnCerrar = new Button();
            panelEstados = new Panel();
            panelEstadoMqtt = new Panel();
            lblEstadoMqtt = new Label();
            panelEstadoCorreo = new Panel();
            lblEstadoCorreo = new Label();
            panelEstadoModbus = new Panel();
            lblEstadoModbus = new Label();
            panelNavegacion = new Panel();
            btnNavCorreo = new Button();
            btnNavMqtt = new Button();
            btnNavModbus = new Button();
            btnNavSinoptico = new Button();
            panelLog = new Panel();
            rtbLog = new RichTextBox();
            btnLimpiarLog = new Button();
            tabControlPrincipal = new TabControl();
            tabPageCorreo = new TabPage();
            grpConfigCorreo = new GroupBox();
            lblEwsServerUrl = new Label();
            txtEwsServerUrl = new TextBox();
            lblEwsUsuario = new Label();
            txtEwsUsuario = new TextBox();
            lblEwsPassword = new Label();
            txtEwsPassword = new TextBox();
            tabPageMqtt = new TabPage();
            grpConexionMqtt = new GroupBox();
            lblMqttProtocolo = new Label();
            cmbMqttProtocolo = new ComboBox();
            lblMqttHost = new Label();
            txtMqttHost = new TextBox();
            lblMqttPuerto = new Label();
            txtMqttPuerto = new TextBox();
            lblMqttClientId = new Label();
            txtMqttClientId = new TextBox();
            lblMqttUsuario = new Label();
            txtMqttUsuario = new TextBox();
            lblMqttPassword = new Label();
            txtMqttPassword = new TextBox();
            chkMqttSslTls = new CheckBox();
            btnMqttConectar = new Button();
            btnMqttDesconectar = new Button();
            tabPageModbus = new TabPage();
            grpOperacionesModbus = new GroupBox();
            lblModbusDireccionRegistro = new Label();
            txtModbusDireccionRegistro = new TextBox();
            lblModbusValorEscritura = new Label();
            txtModbusValorEscritura = new TextBox();
            btnModbusLeerRegistros = new Button();
            btnModbusEscribirRegistro = new Button();
            grpConexionModbus = new GroupBox();
            lblModbusIp = new Label();
            txtModbusIp = new TextBox();
            lblModbusPuerto = new Label();
            nudModbusPuerto = new NumericUpDown();
            lblModbusDeviceId = new Label();
            nudModbusDeviceId = new NumericUpDown();
            btnModbusConectar = new Button();
            btnModbusDesconectar = new Button();
            dgvModbusResultados = new DataGridView();
            grpConfigModbus = new GroupBox();
            cmbModbusConfig = new ComboBox();
            txtModbusConfigNombre = new TextBox();
            btnModbusGuardarConfig = new Button();
            btnModbusNuevaConfig = new Button();
            tabPageSinoptico = new TabPage();
            lblTopicosActivos = new Label();
            lblUltimaActualizacion = new Label();
            grpDatosSinoptico = new GroupBox();
            txtSinopticoFiltro = new TextBox();
            btnSinopticoActualizar = new Button();
            dgvSinopticoDatos = new DataGridView();
            timer1 = new System.Windows.Forms.Timer(components);
            panelBarraSuperior.SuspendLayout();
            panelEstados.SuspendLayout();
            panelNavegacion.SuspendLayout();
            panelLog.SuspendLayout();
            tabControlPrincipal.SuspendLayout();
            tabPageCorreo.SuspendLayout();
            grpConfigCorreo.SuspendLayout();
            tabPageMqtt.SuspendLayout();
            grpConexionMqtt.SuspendLayout();
            tabPageModbus.SuspendLayout();
            grpOperacionesModbus.SuspendLayout();
            grpConexionModbus.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)nudModbusPuerto).BeginInit();
            ((System.ComponentModel.ISupportInitialize)nudModbusDeviceId).BeginInit();
            ((System.ComponentModel.ISupportInitialize)dgvModbusResultados).BeginInit();
            grpConfigModbus.SuspendLayout();
            tabPageSinoptico.SuspendLayout();
            grpDatosSinoptico.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvSinopticoDatos).BeginInit();
            SuspendLayout();
            // 
            // panelBarraSuperior
            // 
            panelBarraSuperior.BackColor = Color.FromArgb(30, 30, 30);
            panelBarraSuperior.Controls.Add(lblTituloApp);
            panelBarraSuperior.Controls.Add(btnCerrar);
            panelBarraSuperior.Dock = DockStyle.Top;
            panelBarraSuperior.Location = new Point(0, 0);
            panelBarraSuperior.Margin = new Padding(4, 5, 4, 5);
            panelBarraSuperior.Name = "panelBarraSuperior";
            panelBarraSuperior.Size = new Size(1920, 94);
            panelBarraSuperior.TabIndex = 0;
            // 
            // lblTituloApp
            // 
            lblTituloApp.AutoSize = true;
            lblTituloApp.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            lblTituloApp.ForeColor = Color.White;
            lblTituloApp.Location = new Point(25, 23);
            lblTituloApp.Margin = new Padding(4, 0, 4, 0);
            lblTituloApp.Name = "lblTituloApp";
            lblTituloApp.Size = new Size(550, 48);
            lblTituloApp.TabIndex = 0;
            lblTituloApp.Text = "Control de Producción ENAGAS";
            // 
            // btnCerrar
            // 
            btnCerrar.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnCerrar.BackColor = Color.FromArgb(209, 17, 65);
            btnCerrar.FlatAppearance.BorderSize = 0;
            btnCerrar.FlatStyle = FlatStyle.Flat;
            btnCerrar.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnCerrar.ForeColor = Color.White;
            btnCerrar.Location = new Point(1858, 23);
            btnCerrar.Margin = new Padding(4, 5, 4, 5);
            btnCerrar.Name = "btnCerrar";
            btnCerrar.Size = new Size(50, 47);
            btnCerrar.TabIndex = 1;
            btnCerrar.Text = "✕";
            btnCerrar.UseVisualStyleBackColor = false;
            btnCerrar.Click += btnCerrar_Click;
            // 
            // panelEstados
            // 
            panelEstados.BackColor = Color.FromArgb(50, 50, 50);
            panelEstados.Controls.Add(panelEstadoMqtt);
            panelEstados.Controls.Add(lblEstadoMqtt);
            panelEstados.Controls.Add(panelEstadoCorreo);
            panelEstados.Controls.Add(lblEstadoCorreo);
            panelEstados.Controls.Add(panelEstadoModbus);
            panelEstados.Controls.Add(lblEstadoModbus);
            panelEstados.Dock = DockStyle.Top;
            panelEstados.Location = new Point(0, 94);
            panelEstados.Margin = new Padding(4, 5, 4, 5);
            panelEstados.Name = "panelEstados";
            panelEstados.Size = new Size(1920, 78);
            panelEstados.TabIndex = 1;
            // 
            // panelEstadoMqtt
            // 
            panelEstadoMqtt.BackColor = Color.Gray;
            panelEstadoMqtt.Location = new Point(62, 23);
            panelEstadoMqtt.Margin = new Padding(4, 5, 4, 5);
            panelEstadoMqtt.Name = "panelEstadoMqtt";
            panelEstadoMqtt.Size = new Size(25, 31);
            panelEstadoMqtt.TabIndex = 0;
            // 
            // lblEstadoMqtt
            // 
            lblEstadoMqtt.AutoSize = true;
            lblEstadoMqtt.Font = new Font("Segoe UI", 10F);
            lblEstadoMqtt.ForeColor = Color.White;
            lblEstadoMqtt.Location = new Point(100, 27);
            lblEstadoMqtt.Margin = new Padding(4, 0, 4, 0);
            lblEstadoMqtt.Name = "lblEstadoMqtt";
            lblEstadoMqtt.Size = new Size(197, 28);
            lblEstadoMqtt.TabIndex = 1;
            lblEstadoMqtt.Text = "MQTT: Desconectado";
            // 
            // panelEstadoCorreo
            // 
            panelEstadoCorreo.BackColor = Color.Gray;
            panelEstadoCorreo.Location = new Point(312, 23);
            panelEstadoCorreo.Margin = new Padding(4, 5, 4, 5);
            panelEstadoCorreo.Name = "panelEstadoCorreo";
            panelEstadoCorreo.Size = new Size(25, 31);
            panelEstadoCorreo.TabIndex = 2;
            // 
            // lblEstadoCorreo
            // 
            lblEstadoCorreo.AutoSize = true;
            lblEstadoCorreo.Font = new Font("Segoe UI", 10F);
            lblEstadoCorreo.ForeColor = Color.White;
            lblEstadoCorreo.Location = new Point(350, 27);
            lblEstadoCorreo.Margin = new Padding(4, 0, 4, 0);
            lblEstadoCorreo.Name = "lblEstadoCorreo";
            lblEstadoCorreo.Size = new Size(205, 28);
            lblEstadoCorreo.TabIndex = 3;
            lblEstadoCorreo.Text = "Correo: Desconectado";
            // 
            // panelEstadoModbus
            // 
            panelEstadoModbus.BackColor = Color.Gray;
            panelEstadoModbus.Location = new Point(562, 23);
            panelEstadoModbus.Margin = new Padding(4, 5, 4, 5);
            panelEstadoModbus.Name = "panelEstadoModbus";
            panelEstadoModbus.Size = new Size(25, 31);
            panelEstadoModbus.TabIndex = 4;
            // 
            // lblEstadoModbus
            // 
            lblEstadoModbus.AutoSize = true;
            lblEstadoModbus.Font = new Font("Segoe UI", 10F);
            lblEstadoModbus.ForeColor = Color.White;
            lblEstadoModbus.Location = new Point(600, 27);
            lblEstadoModbus.Margin = new Padding(4, 0, 4, 0);
            lblEstadoModbus.Name = "lblEstadoModbus";
            lblEstadoModbus.Size = new Size(218, 28);
            lblEstadoModbus.TabIndex = 5;
            lblEstadoModbus.Text = "Modbus: Desconectado";
            // 
            // panelNavegacion
            // 
            panelNavegacion.BackColor = Color.FromArgb(60, 63, 65);
            panelNavegacion.Controls.Add(btnNavCorreo);
            panelNavegacion.Controls.Add(btnNavMqtt);
            panelNavegacion.Controls.Add(btnNavModbus);
            panelNavegacion.Controls.Add(btnNavSinoptico);
            panelNavegacion.Dock = DockStyle.Top;
            panelNavegacion.Location = new Point(0, 172);
            panelNavegacion.Margin = new Padding(4, 5, 4, 5);
            panelNavegacion.Name = "panelNavegacion";
            panelNavegacion.Size = new Size(1920, 94);
            panelNavegacion.TabIndex = 2;
            // 
            // btnNavCorreo
            // 
            btnNavCorreo.BackColor = Color.FromArgb(0, 122, 204);
            btnNavCorreo.FlatAppearance.BorderSize = 0;
            btnNavCorreo.FlatStyle = FlatStyle.Flat;
            btnNavCorreo.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnNavCorreo.ForeColor = Color.White;
            btnNavCorreo.Location = new Point(62, 23);
            btnNavCorreo.Margin = new Padding(4, 5, 4, 5);
            btnNavCorreo.Name = "btnNavCorreo";
            btnNavCorreo.Size = new Size(250, 47);
            btnNavCorreo.TabIndex = 0;
            btnNavCorreo.Text = "📧 CORREO";
            btnNavCorreo.UseVisualStyleBackColor = false;
            btnNavCorreo.Click += btnNavCorreo_Click;
            // 
            // btnNavMqtt
            // 
            btnNavMqtt.BackColor = Color.FromArgb(60, 63, 65);
            btnNavMqtt.FlatAppearance.BorderSize = 0;
            btnNavMqtt.FlatStyle = FlatStyle.Flat;
            btnNavMqtt.Font = new Font("Segoe UI", 12F);
            btnNavMqtt.ForeColor = Color.White;
            btnNavMqtt.Location = new Point(338, 23);
            btnNavMqtt.Margin = new Padding(4, 5, 4, 5);
            btnNavMqtt.Name = "btnNavMqtt";
            btnNavMqtt.Size = new Size(250, 47);
            btnNavMqtt.TabIndex = 1;
            btnNavMqtt.Text = "🌐 MQTT";
            btnNavMqtt.UseVisualStyleBackColor = false;
            btnNavMqtt.Click += btnNavMqtt_Click;
            // 
            // btnNavModbus
            // 
            btnNavModbus.BackColor = Color.FromArgb(60, 63, 65);
            btnNavModbus.FlatAppearance.BorderSize = 0;
            btnNavModbus.FlatStyle = FlatStyle.Flat;
            btnNavModbus.Font = new Font("Segoe UI", 12F);
            btnNavModbus.ForeColor = Color.White;
            btnNavModbus.Location = new Point(612, 23);
            btnNavModbus.Margin = new Padding(4, 5, 4, 5);
            btnNavModbus.Name = "btnNavModbus";
            btnNavModbus.Size = new Size(250, 47);
            btnNavModbus.TabIndex = 2;
            btnNavModbus.Text = "⚙️ MODBUS";
            btnNavModbus.UseVisualStyleBackColor = false;
            btnNavModbus.Click += btnNavModbus_Click;
            // 
            // btnNavSinoptico
            // 
            btnNavSinoptico.BackColor = Color.FromArgb(60, 63, 65);
            btnNavSinoptico.FlatAppearance.BorderSize = 0;
            btnNavSinoptico.FlatStyle = FlatStyle.Flat;
            btnNavSinoptico.Font = new Font("Segoe UI", 12F);
            btnNavSinoptico.ForeColor = Color.White;
            btnNavSinoptico.Location = new Point(888, 23);
            btnNavSinoptico.Margin = new Padding(4, 5, 4, 5);
            btnNavSinoptico.Name = "btnNavSinoptico";
            btnNavSinoptico.Size = new Size(250, 47);
            btnNavSinoptico.TabIndex = 3;
            btnNavSinoptico.Text = "📊 SINÓPTICO";
            btnNavSinoptico.UseVisualStyleBackColor = false;
            btnNavSinoptico.Click += btnNavSinoptico_Click;
            // 
            // panelLog
            // 
            panelLog.BackColor = Color.FromArgb(40, 40, 40);
            panelLog.Controls.Add(rtbLog);
            panelLog.Controls.Add(btnLimpiarLog);
            panelLog.Dock = DockStyle.Bottom;
            panelLog.Location = new Point(0, 894);
            panelLog.Margin = new Padding(4, 5, 4, 5);
            panelLog.Name = "panelLog";
            panelLog.Size = new Size(1920, 312);
            panelLog.TabIndex = 4;
            // 
            // rtbLog
            // 
            rtbLog.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            rtbLog.BackColor = Color.FromArgb(30, 30, 30);
            rtbLog.BorderStyle = BorderStyle.None;
            rtbLog.Font = new Font("Consolas", 9F);
            rtbLog.ForeColor = Color.LightGray;
            rtbLog.Location = new Point(25, 31);
            rtbLog.Margin = new Padding(4, 5, 4, 5);
            rtbLog.Name = "rtbLog";
            rtbLog.ReadOnly = true;
            rtbLog.Size = new Size(1762, 250);
            rtbLog.TabIndex = 0;
            rtbLog.Text = "=== LOG DEL SISTEMA ===\nSistema iniciado correctamente...\n";
            // 
            // btnLimpiarLog
            // 
            btnLimpiarLog.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnLimpiarLog.BackColor = Color.FromArgb(209, 17, 65);
            btnLimpiarLog.FlatAppearance.BorderSize = 0;
            btnLimpiarLog.FlatStyle = FlatStyle.Flat;
            btnLimpiarLog.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnLimpiarLog.ForeColor = Color.White;
            btnLimpiarLog.Location = new Point(1808, 31);
            btnLimpiarLog.Margin = new Padding(4, 5, 4, 5);
            btnLimpiarLog.Name = "btnLimpiarLog";
            btnLimpiarLog.Size = new Size(100, 47);
            btnLimpiarLog.TabIndex = 1;
            btnLimpiarLog.Text = "Limpiar";
            btnLimpiarLog.UseVisualStyleBackColor = false;
            btnLimpiarLog.Click += btnLimpiarLog_Click;
            // 
            // tabControlPrincipal
            // 
            tabControlPrincipal.Controls.Add(tabPageCorreo);
            tabControlPrincipal.Controls.Add(tabPageMqtt);
            tabControlPrincipal.Controls.Add(tabPageModbus);
            tabControlPrincipal.Controls.Add(tabPageSinoptico);
            tabControlPrincipal.Dock = DockStyle.Fill;
            tabControlPrincipal.Location = new Point(0, 266);
            tabControlPrincipal.Margin = new Padding(4, 5, 4, 5);
            tabControlPrincipal.Name = "tabControlPrincipal";
            tabControlPrincipal.SelectedIndex = 0;
            tabControlPrincipal.Size = new Size(1920, 628);
            tabControlPrincipal.TabIndex = 5;
            // 
            // tabPageCorreo
            // 
            tabPageCorreo.BackColor = Color.FromArgb(45, 45, 48);
            tabPageCorreo.Controls.Add(grpConfigCorreo);
            tabPageCorreo.Location = new Point(4, 34);
            tabPageCorreo.Margin = new Padding(4, 5, 4, 5);
            tabPageCorreo.Name = "tabPageCorreo";
            tabPageCorreo.Padding = new Padding(4, 5, 4, 5);
            tabPageCorreo.Size = new Size(1912, 590);
            tabPageCorreo.TabIndex = 0;
            tabPageCorreo.Text = "Correo";
            // 
            // grpConfigCorreo
            // 
            grpConfigCorreo.Controls.Add(lblEwsServerUrl);
            grpConfigCorreo.Controls.Add(txtEwsServerUrl);
            grpConfigCorreo.Controls.Add(lblEwsUsuario);
            grpConfigCorreo.Controls.Add(txtEwsUsuario);
            grpConfigCorreo.Controls.Add(lblEwsPassword);
            grpConfigCorreo.Controls.Add(txtEwsPassword);
            grpConfigCorreo.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            grpConfigCorreo.ForeColor = Color.White;
            grpConfigCorreo.Location = new Point(62, 47);
            grpConfigCorreo.Margin = new Padding(4, 5, 4, 5);
            grpConfigCorreo.Name = "grpConfigCorreo";
            grpConfigCorreo.Padding = new Padding(4, 5, 4, 5);
            grpConfigCorreo.Size = new Size(1000, 469);
            grpConfigCorreo.TabIndex = 0;
            grpConfigCorreo.TabStop = false;
            grpConfigCorreo.Text = "Configuración Exchange (EWS)";
            // 
            // lblEwsServerUrl
            // 
            lblEwsServerUrl.AutoSize = true;
            lblEwsServerUrl.Font = new Font("Segoe UI", 11F);
            lblEwsServerUrl.ForeColor = Color.White;
            lblEwsServerUrl.Location = new Point(38, 78);
            lblEwsServerUrl.Margin = new Padding(4, 0, 4, 0);
            lblEwsServerUrl.Name = "lblEwsServerUrl";
            lblEwsServerUrl.Size = new Size(180, 30);
            lblEwsServerUrl.TabIndex = 0;
            lblEwsServerUrl.Text = "URL del Servidor:";
            // 
            // txtEwsServerUrl
            // 
            txtEwsServerUrl.BackColor = Color.FromArgb(60, 63, 65);
            txtEwsServerUrl.BorderStyle = BorderStyle.FixedSingle;
            txtEwsServerUrl.Font = new Font("Segoe UI", 11F);
            txtEwsServerUrl.ForeColor = Color.White;
            txtEwsServerUrl.Location = new Point(250, 75);
            txtEwsServerUrl.Margin = new Padding(4, 5, 4, 5);
            txtEwsServerUrl.Name = "txtEwsServerUrl";
            txtEwsServerUrl.Size = new Size(687, 37);
            txtEwsServerUrl.TabIndex = 1;
            txtEwsServerUrl.Text = "https://outlook.office365.com/EWS/Exchange.asmx";
            // 
            // lblEwsUsuario
            // 
            lblEwsUsuario.AutoSize = true;
            lblEwsUsuario.Font = new Font("Segoe UI", 11F);
            lblEwsUsuario.ForeColor = Color.White;
            lblEwsUsuario.Location = new Point(38, 156);
            lblEwsUsuario.Margin = new Padding(4, 0, 4, 0);
            lblEwsUsuario.Name = "lblEwsUsuario";
            lblEwsUsuario.Size = new Size(91, 30);
            lblEwsUsuario.TabIndex = 2;
            lblEwsUsuario.Text = "Usuario:";
            // 
            // txtEwsUsuario
            // 
            txtEwsUsuario.BackColor = Color.FromArgb(60, 63, 65);
            txtEwsUsuario.BorderStyle = BorderStyle.FixedSingle;
            txtEwsUsuario.Font = new Font("Segoe UI", 11F);
            txtEwsUsuario.ForeColor = Color.White;
            txtEwsUsuario.Location = new Point(250, 153);
            txtEwsUsuario.Margin = new Padding(4, 5, 4, 5);
            txtEwsUsuario.Name = "txtEwsUsuario";
            txtEwsUsuario.Size = new Size(500, 37);
            txtEwsUsuario.TabIndex = 3;
            // 
            // lblEwsPassword
            // 
            lblEwsPassword.AutoSize = true;
            lblEwsPassword.Font = new Font("Segoe UI", 11F);
            lblEwsPassword.ForeColor = Color.White;
            lblEwsPassword.Location = new Point(38, 234);
            lblEwsPassword.Margin = new Padding(4, 0, 4, 0);
            lblEwsPassword.Name = "lblEwsPassword";
            lblEwsPassword.Size = new Size(127, 30);
            lblEwsPassword.TabIndex = 4;
            lblEwsPassword.Text = "Contraseña:";
            // 
            // txtEwsPassword
            // 
            txtEwsPassword.BackColor = Color.FromArgb(60, 63, 65);
            txtEwsPassword.BorderStyle = BorderStyle.FixedSingle;
            txtEwsPassword.Font = new Font("Segoe UI", 11F);
            txtEwsPassword.ForeColor = Color.White;
            txtEwsPassword.Location = new Point(250, 231);
            txtEwsPassword.Margin = new Padding(4, 5, 4, 5);
            txtEwsPassword.Name = "txtEwsPassword";
            txtEwsPassword.PasswordChar = '*';
            txtEwsPassword.Size = new Size(500, 37);
            txtEwsPassword.TabIndex = 5;
            // 
            // tabPageMqtt
            // 
            tabPageMqtt.BackColor = Color.FromArgb(45, 45, 48);
            tabPageMqtt.Controls.Add(grpConexionMqtt);
            tabPageMqtt.Location = new Point(4, 34);
            tabPageMqtt.Margin = new Padding(4, 5, 4, 5);
            tabPageMqtt.Name = "tabPageMqtt";
            tabPageMqtt.Padding = new Padding(4, 5, 4, 5);
            tabPageMqtt.Size = new Size(1912, 590);
            tabPageMqtt.TabIndex = 1;
            tabPageMqtt.Text = "MQTT";
            // 
            // grpConexionMqtt
            // 
            grpConexionMqtt.Controls.Add(lblMqttProtocolo);
            grpConexionMqtt.Controls.Add(cmbMqttProtocolo);
            grpConexionMqtt.Controls.Add(lblMqttHost);
            grpConexionMqtt.Controls.Add(txtMqttHost);
            grpConexionMqtt.Controls.Add(lblMqttPuerto);
            grpConexionMqtt.Controls.Add(txtMqttPuerto);
            grpConexionMqtt.Controls.Add(lblMqttClientId);
            grpConexionMqtt.Controls.Add(txtMqttClientId);
            grpConexionMqtt.Controls.Add(lblMqttUsuario);
            grpConexionMqtt.Controls.Add(txtMqttUsuario);
            grpConexionMqtt.Controls.Add(lblMqttPassword);
            grpConexionMqtt.Controls.Add(txtMqttPassword);
            grpConexionMqtt.Controls.Add(chkMqttSslTls);
            grpConexionMqtt.Controls.Add(btnMqttConectar);
            grpConexionMqtt.Controls.Add(btnMqttDesconectar);
            grpConexionMqtt.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            grpConexionMqtt.ForeColor = Color.White;
            grpConexionMqtt.Location = new Point(62, 47);
            grpConexionMqtt.Margin = new Padding(4, 5, 4, 5);
            grpConexionMqtt.Name = "grpConexionMqtt";
            grpConexionMqtt.Padding = new Padding(4, 5, 4, 5);
            grpConexionMqtt.Size = new Size(1000, 500);
            grpConexionMqtt.TabIndex = 1;
            grpConexionMqtt.TabStop = false;
            grpConexionMqtt.Text = "Conexión MQTT";
            // 
            // lblMqttProtocolo
            // 
            lblMqttProtocolo.AutoSize = true;
            lblMqttProtocolo.Font = new Font("Segoe UI", 11F);
            lblMqttProtocolo.ForeColor = Color.White;
            lblMqttProtocolo.Location = new Point(38, 78);
            lblMqttProtocolo.Margin = new Padding(4, 0, 4, 0);
            lblMqttProtocolo.Name = "lblMqttProtocolo";
            lblMqttProtocolo.Size = new Size(112, 30);
            lblMqttProtocolo.TabIndex = 0;
            lblMqttProtocolo.Text = "Protocolo:";
            // 
            // cmbMqttProtocolo
            // 
            cmbMqttProtocolo.BackColor = Color.FromArgb(60, 63, 65);
            cmbMqttProtocolo.ForeColor = Color.White;
            cmbMqttProtocolo.FormattingEnabled = true;
            cmbMqttProtocolo.Items.AddRange(new object[] { "mqtt://", "mqtts://", "ws://", "wss://" });
            cmbMqttProtocolo.Location = new Point(188, 75);
            cmbMqttProtocolo.Margin = new Padding(4, 5, 4, 5);
            cmbMqttProtocolo.Name = "cmbMqttProtocolo";
            cmbMqttProtocolo.Size = new Size(149, 40);
            cmbMqttProtocolo.TabIndex = 1;
            // 
            // lblMqttHost
            // 
            lblMqttHost.AutoSize = true;
            lblMqttHost.Font = new Font("Segoe UI", 11F);
            lblMqttHost.Location = new Point(360, 78);
            lblMqttHost.Name = "lblMqttHost";
            lblMqttHost.Size = new Size(63, 30);
            lblMqttHost.TabIndex = 2;
            lblMqttHost.Text = "Host:";
            // 
            // txtMqttHost
            // 
            txtMqttHost.BackColor = Color.FromArgb(60, 63, 65);
            txtMqttHost.BorderStyle = BorderStyle.FixedSingle;
            txtMqttHost.Font = new Font("Segoe UI", 11F);
            txtMqttHost.ForeColor = Color.White;
            txtMqttHost.Location = new Point(430, 75);
            txtMqttHost.Name = "txtMqttHost";
            txtMqttHost.Size = new Size(250, 37);
            txtMqttHost.TabIndex = 3;
            // 
            // lblMqttPuerto
            // 
            lblMqttPuerto.AutoSize = true;
            lblMqttPuerto.Font = new Font("Segoe UI", 11F);
            lblMqttPuerto.Location = new Point(700, 78);
            lblMqttPuerto.Name = "lblMqttPuerto";
            lblMqttPuerto.Size = new Size(82, 30);
            lblMqttPuerto.TabIndex = 4;
            lblMqttPuerto.Text = "Puerto:";
            // 
            // txtMqttPuerto
            // 
            txtMqttPuerto.BackColor = Color.FromArgb(60, 63, 65);
            txtMqttPuerto.BorderStyle = BorderStyle.FixedSingle;
            txtMqttPuerto.Font = new Font("Segoe UI", 11F);
            txtMqttPuerto.ForeColor = Color.White;
            txtMqttPuerto.Location = new Point(790, 75);
            txtMqttPuerto.Name = "txtMqttPuerto";
            txtMqttPuerto.Size = new Size(100, 37);
            txtMqttPuerto.TabIndex = 5;
            // 
            // lblMqttClientId
            // 
            lblMqttClientId.AutoSize = true;
            lblMqttClientId.Font = new Font("Segoe UI", 11F);
            lblMqttClientId.Location = new Point(38, 140);
            lblMqttClientId.Name = "lblMqttClientId";
            lblMqttClientId.Size = new Size(100, 30);
            lblMqttClientId.TabIndex = 6;
            lblMqttClientId.Text = "Client ID:";
            // 
            // txtMqttClientId
            // 
            txtMqttClientId.BackColor = Color.FromArgb(60, 63, 65);
            txtMqttClientId.BorderStyle = BorderStyle.FixedSingle;
            txtMqttClientId.Font = new Font("Segoe UI", 11F);
            txtMqttClientId.ForeColor = Color.White;
            txtMqttClientId.Location = new Point(188, 138);
            txtMqttClientId.Name = "txtMqttClientId";
            txtMqttClientId.Size = new Size(400, 37);
            txtMqttClientId.TabIndex = 7;
            // 
            // lblMqttUsuario
            // 
            lblMqttUsuario.AutoSize = true;
            lblMqttUsuario.Font = new Font("Segoe UI", 11F);
            lblMqttUsuario.Location = new Point(38, 200);
            lblMqttUsuario.Name = "lblMqttUsuario";
            lblMqttUsuario.Size = new Size(91, 30);
            lblMqttUsuario.TabIndex = 8;
            lblMqttUsuario.Text = "Usuario:";
            // 
            // txtMqttUsuario
            // 
            txtMqttUsuario.BackColor = Color.FromArgb(60, 63, 65);
            txtMqttUsuario.BorderStyle = BorderStyle.FixedSingle;
            txtMqttUsuario.Font = new Font("Segoe UI", 11F);
            txtMqttUsuario.ForeColor = Color.White;
            txtMqttUsuario.Location = new Point(188, 198);
            txtMqttUsuario.Name = "txtMqttUsuario";
            txtMqttUsuario.Size = new Size(400, 37);
            txtMqttUsuario.TabIndex = 9;
            // 
            // lblMqttPassword
            // 
            lblMqttPassword.AutoSize = true;
            lblMqttPassword.Font = new Font("Segoe UI", 11F);
            lblMqttPassword.Location = new Point(38, 260);
            lblMqttPassword.Name = "lblMqttPassword";
            lblMqttPassword.Size = new Size(127, 30);
            lblMqttPassword.TabIndex = 10;
            lblMqttPassword.Text = "Contraseña:";
            // 
            // txtMqttPassword
            // 
            txtMqttPassword.BackColor = Color.FromArgb(60, 63, 65);
            txtMqttPassword.BorderStyle = BorderStyle.FixedSingle;
            txtMqttPassword.Font = new Font("Segoe UI", 11F);
            txtMqttPassword.ForeColor = Color.White;
            txtMqttPassword.Location = new Point(188, 258);
            txtMqttPassword.Name = "txtMqttPassword";
            txtMqttPassword.PasswordChar = '*';
            txtMqttPassword.Size = new Size(400, 37);
            txtMqttPassword.TabIndex = 11;
            // 
            // chkMqttSslTls
            // 
            chkMqttSslTls.AutoSize = true;
            chkMqttSslTls.Font = new Font("Segoe UI", 11F);
            chkMqttSslTls.Location = new Point(42, 330);
            chkMqttSslTls.Name = "chkMqttSslTls";
            chkMqttSslTls.Size = new Size(203, 34);
            chkMqttSslTls.TabIndex = 12;
            chkMqttSslTls.Text = "Habilitar SSL/TLS";
            chkMqttSslTls.UseVisualStyleBackColor = true;
            // 
            // btnMqttConectar
            // 
            btnMqttConectar.BackColor = Color.FromArgb(0, 122, 204);
            btnMqttConectar.FlatAppearance.BorderSize = 0;
            btnMqttConectar.FlatStyle = FlatStyle.Flat;
            btnMqttConectar.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnMqttConectar.ForeColor = Color.White;
            btnMqttConectar.Location = new Point(42, 400);
            btnMqttConectar.Name = "btnMqttConectar";
            btnMqttConectar.Size = new Size(150, 47);
            btnMqttConectar.TabIndex = 13;
            btnMqttConectar.Text = "Conectar";
            btnMqttConectar.UseVisualStyleBackColor = false;
            btnMqttConectar.Click += btnMqttConectar_Click;
            // 
            // btnMqttDesconectar
            // 
            btnMqttDesconectar.BackColor = Color.FromArgb(209, 17, 65);
            btnMqttDesconectar.FlatAppearance.BorderSize = 0;
            btnMqttDesconectar.FlatStyle = FlatStyle.Flat;
            btnMqttDesconectar.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnMqttDesconectar.ForeColor = Color.White;
            btnMqttDesconectar.Location = new Point(212, 400);
            btnMqttDesconectar.Name = "btnMqttDesconectar";
            btnMqttDesconectar.Size = new Size(150, 47);
            btnMqttDesconectar.TabIndex = 14;
            btnMqttDesconectar.Text = "Desconectar";
            btnMqttDesconectar.UseVisualStyleBackColor = false;
            // 
            // tabPageModbus
            // 
            tabPageModbus.BackColor = Color.FromArgb(45, 45, 48);
            tabPageModbus.Controls.Add(grpOperacionesModbus);
            tabPageModbus.Controls.Add(grpConexionModbus);
            tabPageModbus.Controls.Add(dgvModbusResultados);
            tabPageModbus.Controls.Add(grpConfigModbus);
            tabPageModbus.Location = new Point(4, 34);
            tabPageModbus.Margin = new Padding(4, 5, 4, 5);
            tabPageModbus.Name = "tabPageModbus";
            tabPageModbus.Size = new Size(1912, 590);
            tabPageModbus.TabIndex = 2;
            tabPageModbus.Text = "Modbus";
            // 
            // grpOperacionesModbus
            // 
            grpOperacionesModbus.Controls.Add(lblModbusDireccionRegistro);
            grpOperacionesModbus.Controls.Add(txtModbusDireccionRegistro);
            grpOperacionesModbus.Controls.Add(lblModbusValorEscritura);
            grpOperacionesModbus.Controls.Add(txtModbusValorEscritura);
            grpOperacionesModbus.Controls.Add(btnModbusLeerRegistros);
            grpOperacionesModbus.Controls.Add(btnModbusEscribirRegistro);
            grpOperacionesModbus.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            grpOperacionesModbus.ForeColor = Color.White;
            grpOperacionesModbus.Location = new Point(62, 260);
            grpOperacionesModbus.Name = "grpOperacionesModbus";
            grpOperacionesModbus.Size = new Size(550, 180);
            grpOperacionesModbus.TabIndex = 3;
            grpOperacionesModbus.TabStop = false;
            grpOperacionesModbus.Text = "Operaciones";
            // 
            // lblModbusDireccionRegistro
            // 
            lblModbusDireccionRegistro.AutoSize = true;
            lblModbusDireccionRegistro.Font = new Font("Segoe UI", 11F);
            lblModbusDireccionRegistro.Location = new Point(20, 50);
            lblModbusDireccionRegistro.Name = "lblModbusDireccionRegistro";
            lblModbusDireccionRegistro.Size = new Size(108, 30);
            lblModbusDireccionRegistro.TabIndex = 0;
            lblModbusDireccionRegistro.Text = "Dirección:";
            // 
            // txtModbusDireccionRegistro
            // 
            txtModbusDireccionRegistro.BackColor = Color.FromArgb(60, 63, 65);
            txtModbusDireccionRegistro.BorderStyle = BorderStyle.FixedSingle;
            txtModbusDireccionRegistro.Font = new Font("Segoe UI", 11F);
            txtModbusDireccionRegistro.ForeColor = Color.White;
            txtModbusDireccionRegistro.Location = new Point(140, 48);
            txtModbusDireccionRegistro.Name = "txtModbusDireccionRegistro";
            txtModbusDireccionRegistro.Size = new Size(120, 37);
            txtModbusDireccionRegistro.TabIndex = 1;
            // 
            // lblModbusValorEscritura
            // 
            lblModbusValorEscritura.AutoSize = true;
            lblModbusValorEscritura.Font = new Font("Segoe UI", 11F);
            lblModbusValorEscritura.Location = new Point(280, 50);
            lblModbusValorEscritura.Name = "lblModbusValorEscritura";
            lblModbusValorEscritura.Size = new Size(67, 30);
            lblModbusValorEscritura.TabIndex = 2;
            lblModbusValorEscritura.Text = "Valor:";
            // 
            // txtModbusValorEscritura
            // 
            txtModbusValorEscritura.BackColor = Color.FromArgb(60, 63, 65);
            txtModbusValorEscritura.BorderStyle = BorderStyle.FixedSingle;
            txtModbusValorEscritura.Font = new Font("Segoe UI", 11F);
            txtModbusValorEscritura.ForeColor = Color.White;
            txtModbusValorEscritura.Location = new Point(360, 48);
            txtModbusValorEscritura.Name = "txtModbusValorEscritura";
            txtModbusValorEscritura.Size = new Size(120, 37);
            txtModbusValorEscritura.TabIndex = 3;
            // 
            // btnModbusLeerRegistros
            // 
            btnModbusLeerRegistros.BackColor = Color.FromArgb(0, 122, 204);
            btnModbusLeerRegistros.FlatAppearance.BorderSize = 0;
            btnModbusLeerRegistros.FlatStyle = FlatStyle.Flat;
            btnModbusLeerRegistros.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnModbusLeerRegistros.Location = new Point(24, 110);
            btnModbusLeerRegistros.Name = "btnModbusLeerRegistros";
            btnModbusLeerRegistros.Size = new Size(200, 47);
            btnModbusLeerRegistros.TabIndex = 4;
            btnModbusLeerRegistros.Text = "Leer Registros";
            btnModbusLeerRegistros.UseVisualStyleBackColor = false;
            // 
            // btnModbusEscribirRegistro
            // 
            btnModbusEscribirRegistro.BackColor = Color.FromArgb(209, 17, 65);
            btnModbusEscribirRegistro.FlatAppearance.BorderSize = 0;
            btnModbusEscribirRegistro.FlatStyle = FlatStyle.Flat;
            btnModbusEscribirRegistro.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnModbusEscribirRegistro.Location = new Point(240, 110);
            btnModbusEscribirRegistro.Name = "btnModbusEscribirRegistro";
            btnModbusEscribirRegistro.Size = new Size(200, 47);
            btnModbusEscribirRegistro.TabIndex = 5;
            btnModbusEscribirRegistro.Text = "Escribir Registro";
            btnModbusEscribirRegistro.UseVisualStyleBackColor = false;
            // 
            // grpConexionModbus
            // 
            grpConexionModbus.Controls.Add(lblModbusIp);
            grpConexionModbus.Controls.Add(txtModbusIp);
            grpConexionModbus.Controls.Add(lblModbusPuerto);
            grpConexionModbus.Controls.Add(nudModbusPuerto);
            grpConexionModbus.Controls.Add(lblModbusDeviceId);
            grpConexionModbus.Controls.Add(nudModbusDeviceId);
            grpConexionModbus.Controls.Add(btnModbusConectar);
            grpConexionModbus.Controls.Add(btnModbusDesconectar);
            grpConexionModbus.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            grpConexionModbus.ForeColor = Color.White;
            grpConexionModbus.Location = new Point(628, 47);
            grpConexionModbus.Name = "grpConexionModbus";
            grpConexionModbus.Size = new Size(434, 300);
            grpConexionModbus.TabIndex = 2;
            grpConexionModbus.TabStop = false;
            grpConexionModbus.Text = "Conexión TCP";
            // 
            // lblModbusIp
            // 
            lblModbusIp.AutoSize = true;
            lblModbusIp.Font = new Font("Segoe UI", 11F);
            lblModbusIp.Location = new Point(20, 50);
            lblModbusIp.Name = "lblModbusIp";
            lblModbusIp.Size = new Size(132, 30);
            lblModbusIp.TabIndex = 0;
            lblModbusIp.Text = "Dirección IP:";
            // 
            // txtModbusIp
            // 
            txtModbusIp.BackColor = Color.FromArgb(60, 63, 65);
            txtModbusIp.BorderStyle = BorderStyle.FixedSingle;
            txtModbusIp.Font = new Font("Segoe UI", 11F);
            txtModbusIp.ForeColor = Color.White;
            txtModbusIp.Location = new Point(170, 48);
            txtModbusIp.Name = "txtModbusIp";
            txtModbusIp.Size = new Size(200, 37);
            txtModbusIp.TabIndex = 1;
            // 
            // lblModbusPuerto
            // 
            lblModbusPuerto.AutoSize = true;
            lblModbusPuerto.Font = new Font("Segoe UI", 11F);
            lblModbusPuerto.Location = new Point(20, 100);
            lblModbusPuerto.Name = "lblModbusPuerto";
            lblModbusPuerto.Size = new Size(82, 30);
            lblModbusPuerto.TabIndex = 2;
            lblModbusPuerto.Text = "Puerto:";
            // 
            // nudModbusPuerto
            // 
            nudModbusPuerto.BackColor = Color.FromArgb(60, 63, 65);
            nudModbusPuerto.Font = new Font("Segoe UI", 11F);
            nudModbusPuerto.ForeColor = Color.White;
            nudModbusPuerto.Location = new Point(170, 98);
            nudModbusPuerto.Maximum = new decimal(new int[] { 65535, 0, 0, 0 });
            nudModbusPuerto.Name = "nudModbusPuerto";
            nudModbusPuerto.Size = new Size(120, 37);
            nudModbusPuerto.TabIndex = 3;
            // 
            // lblModbusDeviceId
            // 
            lblModbusDeviceId.AutoSize = true;
            lblModbusDeviceId.Font = new Font("Segoe UI", 11F);
            lblModbusDeviceId.Location = new Point(20, 150);
            lblModbusDeviceId.Name = "lblModbusDeviceId";
            lblModbusDeviceId.Size = new Size(150, 30);
            lblModbusDeviceId.TabIndex = 4;
            lblModbusDeviceId.Text = "ID Dispositivo:";
            // 
            // nudModbusDeviceId
            // 
            nudModbusDeviceId.BackColor = Color.FromArgb(60, 63, 65);
            nudModbusDeviceId.Font = new Font("Segoe UI", 11F);
            nudModbusDeviceId.ForeColor = Color.White;
            nudModbusDeviceId.Location = new Point(170, 148);
            nudModbusDeviceId.Maximum = new decimal(new int[] { 255, 0, 0, 0 });
            nudModbusDeviceId.Name = "nudModbusDeviceId";
            nudModbusDeviceId.Size = new Size(120, 37);
            nudModbusDeviceId.TabIndex = 5;
            // 
            // btnModbusConectar
            // 
            btnModbusConectar.BackColor = Color.FromArgb(0, 122, 204);
            btnModbusConectar.FlatAppearance.BorderSize = 0;
            btnModbusConectar.FlatStyle = FlatStyle.Flat;
            btnModbusConectar.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnModbusConectar.Location = new Point(24, 220);
            btnModbusConectar.Name = "btnModbusConectar";
            btnModbusConectar.Size = new Size(150, 47);
            btnModbusConectar.TabIndex = 6;
            btnModbusConectar.Text = "Conectar";
            btnModbusConectar.UseVisualStyleBackColor = false;
            // 
            // btnModbusDesconectar
            // 
            btnModbusDesconectar.BackColor = Color.FromArgb(209, 17, 65);
            btnModbusDesconectar.FlatAppearance.BorderSize = 0;
            btnModbusDesconectar.FlatStyle = FlatStyle.Flat;
            btnModbusDesconectar.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnModbusDesconectar.Location = new Point(190, 220);
            btnModbusDesconectar.Name = "btnModbusDesconectar";
            btnModbusDesconectar.Size = new Size(150, 47);
            btnModbusDesconectar.TabIndex = 7;
            btnModbusDesconectar.Text = "Desconectar";
            btnModbusDesconectar.UseVisualStyleBackColor = false;
            // 
            // dgvModbusResultados
            // 
            dgvModbusResultados.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dgvModbusResultados.BackgroundColor = Color.FromArgb(60, 63, 65);
            dgvModbusResultados.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvModbusResultados.Location = new Point(1088, 47);
            dgvModbusResultados.Name = "dgvModbusResultados";
            dgvModbusResultados.RowHeadersWidth = 62;
            dgvModbusResultados.RowTemplate.Height = 28;
            dgvModbusResultados.Size = new Size(800, 500);
            dgvModbusResultados.TabIndex = 1;
            // 
            // grpConfigModbus
            // 
            grpConfigModbus.Controls.Add(cmbModbusConfig);
            grpConfigModbus.Controls.Add(txtModbusConfigNombre);
            grpConfigModbus.Controls.Add(btnModbusGuardarConfig);
            grpConfigModbus.Controls.Add(btnModbusNuevaConfig);
            grpConfigModbus.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            grpConfigModbus.ForeColor = Color.White;
            grpConfigModbus.Location = new Point(62, 47);
            grpConfigModbus.Name = "grpConfigModbus";
            grpConfigModbus.Size = new Size(550, 180);
            grpConfigModbus.TabIndex = 0;
            grpConfigModbus.TabStop = false;
            grpConfigModbus.Text = "Configuración";
            // 
            // cmbModbusConfig
            // 
            cmbModbusConfig.BackColor = Color.FromArgb(60, 63, 65);
            cmbModbusConfig.Font = new Font("Segoe UI", 11F);
            cmbModbusConfig.ForeColor = Color.White;
            cmbModbusConfig.FormattingEnabled = true;
            cmbModbusConfig.Location = new Point(24, 50);
            cmbModbusConfig.Name = "cmbModbusConfig";
            cmbModbusConfig.Size = new Size(250, 38);
            cmbModbusConfig.TabIndex = 0;
            // 
            // txtModbusConfigNombre
            // 
            txtModbusConfigNombre.BackColor = Color.FromArgb(60, 63, 65);
            txtModbusConfigNombre.BorderStyle = BorderStyle.FixedSingle;
            txtModbusConfigNombre.Font = new Font("Segoe UI", 11F);
            txtModbusConfigNombre.ForeColor = Color.White;
            txtModbusConfigNombre.Location = new Point(290, 50);
            txtModbusConfigNombre.Name = "txtModbusConfigNombre";
            txtModbusConfigNombre.Size = new Size(250, 37);
            txtModbusConfigNombre.TabIndex = 1;
            // 
            // btnModbusGuardarConfig
            // 
            btnModbusGuardarConfig.BackColor = Color.FromArgb(0, 122, 204);
            btnModbusGuardarConfig.FlatAppearance.BorderSize = 0;
            btnModbusGuardarConfig.FlatStyle = FlatStyle.Flat;
            btnModbusGuardarConfig.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnModbusGuardarConfig.Location = new Point(24, 110);
            btnModbusGuardarConfig.Name = "btnModbusGuardarConfig";
            btnModbusGuardarConfig.Size = new Size(120, 47);
            btnModbusGuardarConfig.TabIndex = 2;
            btnModbusGuardarConfig.Text = "Guardar";
            btnModbusGuardarConfig.UseVisualStyleBackColor = false;
            // 
            // btnModbusNuevaConfig
            // 
            btnModbusNuevaConfig.BackColor = Color.FromArgb(60, 63, 65);
            btnModbusNuevaConfig.FlatStyle = FlatStyle.Flat;
            btnModbusNuevaConfig.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnModbusNuevaConfig.Location = new Point(160, 110);
            btnModbusNuevaConfig.Name = "btnModbusNuevaConfig";
            btnModbusNuevaConfig.Size = new Size(120, 47);
            btnModbusNuevaConfig.TabIndex = 3;
            btnModbusNuevaConfig.Text = "Nuevo";
            btnModbusNuevaConfig.UseVisualStyleBackColor = false;
            // 
            // tabPageSinoptico
            // 
            tabPageSinoptico.BackColor = Color.FromArgb(45, 45, 48);
            tabPageSinoptico.Controls.Add(lblTopicosActivos);
            tabPageSinoptico.Controls.Add(lblUltimaActualizacion);
            tabPageSinoptico.Controls.Add(grpDatosSinoptico);
            tabPageSinoptico.Location = new Point(4, 34);
            tabPageSinoptico.Margin = new Padding(4, 5, 4, 5);
            tabPageSinoptico.Name = "tabPageSinoptico";
            tabPageSinoptico.Size = new Size(1912, 590);
            tabPageSinoptico.TabIndex = 3;
            tabPageSinoptico.Text = "Sinóptico";
            // 
            // lblTopicosActivos
            // 
            lblTopicosActivos.AutoSize = true;
            lblTopicosActivos.Font = new Font("Segoe UI", 10F);
            lblTopicosActivos.ForeColor = Color.White;
            lblTopicosActivos.Location = new Point(62, 580);
            lblTopicosActivos.Name = "lblTopicosActivos";
            lblTopicosActivos.Size = new Size(167, 28);
            lblTopicosActivos.TabIndex = 2;
            lblTopicosActivos.Text = "Tópicos Activos: 0";
            // 
            // lblUltimaActualizacion
            // 
            lblUltimaActualizacion.AutoSize = true;
            lblUltimaActualizacion.Font = new Font("Segoe UI", 10F);
            lblUltimaActualizacion.ForeColor = Color.White;
            lblUltimaActualizacion.Location = new Point(300, 580);
            lblUltimaActualizacion.Name = "lblUltimaActualizacion";
            lblUltimaActualizacion.Size = new Size(216, 28);
            lblUltimaActualizacion.TabIndex = 3;
            lblUltimaActualizacion.Text = "Última Actualización: --";
            // 
            // grpDatosSinoptico
            // 
            grpDatosSinoptico.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            grpDatosSinoptico.Controls.Add(txtSinopticoFiltro);
            grpDatosSinoptico.Controls.Add(btnSinopticoActualizar);
            grpDatosSinoptico.Controls.Add(dgvSinopticoDatos);
            grpDatosSinoptico.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            grpDatosSinoptico.ForeColor = Color.White;
            grpDatosSinoptico.Location = new Point(62, 47);
            grpDatosSinoptico.Name = "grpDatosSinoptico";
            grpDatosSinoptico.Size = new Size(1826, 500);
            grpDatosSinoptico.TabIndex = 1;
            grpDatosSinoptico.TabStop = false;
            grpDatosSinoptico.Text = "Vista de Datos en Tiempo Real";
            // 
            // txtSinopticoFiltro
            // 
            txtSinopticoFiltro.BackColor = Color.FromArgb(60, 63, 65);
            txtSinopticoFiltro.BorderStyle = BorderStyle.FixedSingle;
            txtSinopticoFiltro.Font = new Font("Segoe UI", 11F);
            txtSinopticoFiltro.ForeColor = Color.White;
            txtSinopticoFiltro.Location = new Point(24, 50);
            txtSinopticoFiltro.Name = "txtSinopticoFiltro";
            txtSinopticoFiltro.Size = new Size(400, 37);
            txtSinopticoFiltro.TabIndex = 0;
            // 
            // btnSinopticoActualizar
            // 
            btnSinopticoActualizar.BackColor = Color.FromArgb(0, 122, 204);
            btnSinopticoActualizar.FlatAppearance.BorderSize = 0;
            btnSinopticoActualizar.FlatStyle = FlatStyle.Flat;
            btnSinopticoActualizar.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnSinopticoActualizar.Location = new Point(440, 48);
            btnSinopticoActualizar.Name = "btnSinopticoActualizar";
            btnSinopticoActualizar.Size = new Size(150, 47);
            btnSinopticoActualizar.TabIndex = 1;
            btnSinopticoActualizar.Text = "Actualizar";
            btnSinopticoActualizar.UseVisualStyleBackColor = false;
            // 
            // dgvSinopticoDatos
            // 
            dgvSinopticoDatos.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dgvSinopticoDatos.BackgroundColor = Color.FromArgb(60, 63, 65);
            dgvSinopticoDatos.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvSinopticoDatos.Location = new Point(24, 110);
            dgvSinopticoDatos.Name = "dgvSinopticoDatos";
            dgvSinopticoDatos.RowHeadersWidth = 62;
            dgvSinopticoDatos.RowTemplate.Height = 28;
            dgvSinopticoDatos.Size = new Size(1778, 360);
            dgvSinopticoDatos.TabIndex = 2;
            // 
            // timer1
            // 
            timer1.Enabled = true;
            timer1.Interval = 1000;
            timer1.Tick += timer1_Tick;
            // 
            // MAIN
            // 
            AutoScaleDimensions = new SizeF(10F, 25F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(45, 45, 48);
            ClientSize = new Size(1920, 1206);
            Controls.Add(tabControlPrincipal);
            Controls.Add(panelNavegacion);
            Controls.Add(panelEstados);
            Controls.Add(panelBarraSuperior);
            Controls.Add(panelLog);
            FormBorderStyle = FormBorderStyle.None;
            Margin = new Padding(4, 5, 4, 5);
            Name = "MAIN";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "Control de Producción ENAGAS";
            WindowState = FormWindowState.Maximized;
            panelBarraSuperior.ResumeLayout(false);
            panelBarraSuperior.PerformLayout();
            panelEstados.ResumeLayout(false);
            panelEstados.PerformLayout();
            panelNavegacion.ResumeLayout(false);
            panelLog.ResumeLayout(false);
            tabControlPrincipal.ResumeLayout(false);
            tabPageCorreo.ResumeLayout(false);
            grpConfigCorreo.ResumeLayout(false);
            grpConfigCorreo.PerformLayout();
            tabPageMqtt.ResumeLayout(false);
            grpConexionMqtt.ResumeLayout(false);
            grpConexionMqtt.PerformLayout();
            tabPageModbus.ResumeLayout(false);
            grpOperacionesModbus.ResumeLayout(false);
            grpOperacionesModbus.PerformLayout();
            grpConexionModbus.ResumeLayout(false);
            grpConexionModbus.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)nudModbusPuerto).EndInit();
            ((System.ComponentModel.ISupportInitialize)nudModbusDeviceId).EndInit();
            ((System.ComponentModel.ISupportInitialize)dgvModbusResultados).EndInit();
            grpConfigModbus.ResumeLayout(false);
            grpConfigModbus.PerformLayout();
            tabPageSinoptico.ResumeLayout(false);
            tabPageSinoptico.PerformLayout();
            grpDatosSinoptico.ResumeLayout(false);
            grpDatosSinoptico.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dgvSinopticoDatos).EndInit();
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panelBarraSuperior;
        private System.Windows.Forms.Label lblTituloApp;
        private System.Windows.Forms.Button btnCerrar;
        private System.Windows.Forms.Panel panelEstados;
        private System.Windows.Forms.Panel panelEstadoMqtt;
        private System.Windows.Forms.Label lblEstadoMqtt;
        private System.Windows.Forms.Panel panelEstadoCorreo;
        private System.Windows.Forms.Label lblEstadoCorreo;
        private System.Windows.Forms.Panel panelEstadoModbus;
        private System.Windows.Forms.Label lblEstadoModbus;
        private System.Windows.Forms.Panel panelNavegacion;
        private System.Windows.Forms.Button btnNavCorreo;
        private System.Windows.Forms.Button btnNavMqtt;
        private System.Windows.Forms.Button btnNavModbus;
        private System.Windows.Forms.Button btnNavSinoptico;
        private System.Windows.Forms.Panel panelLog;
        private System.Windows.Forms.RichTextBox rtbLog;
        private System.Windows.Forms.Button btnLimpiarLog;
        private System.Windows.Forms.TabControl tabControlPrincipal;
        private System.Windows.Forms.TabPage tabPageCorreo;
        private System.Windows.Forms.TabPage tabPageMqtt;
        private System.Windows.Forms.TabPage tabPageModbus;
        private System.Windows.Forms.TabPage tabPageSinoptico;
        private System.Windows.Forms.GroupBox grpConfigCorreo;
        private System.Windows.Forms.Label lblEwsServerUrl;
        private System.Windows.Forms.TextBox txtEwsServerUrl;
        private System.Windows.Forms.Label lblEwsUsuario;
        private System.Windows.Forms.TextBox txtEwsUsuario;
        private System.Windows.Forms.Label lblEwsPassword;
        private System.Windows.Forms.TextBox txtEwsPassword;
        private System.Windows.Forms.GroupBox grpConexionMqtt;
        private System.Windows.Forms.Label lblMqttProtocolo;
        private System.Windows.Forms.ComboBox cmbMqttProtocolo;
        private System.Windows.Forms.Label lblMqttHost;
        private System.Windows.Forms.TextBox txtMqttHost;
        private System.Windows.Forms.Label lblMqttPuerto;
        private System.Windows.Forms.TextBox txtMqttPuerto;
        private System.Windows.Forms.Label lblMqttClientId;
        private System.Windows.Forms.TextBox txtMqttClientId;
        private System.Windows.Forms.Label lblMqttUsuario;
        private System.Windows.Forms.TextBox txtMqttUsuario;
        private System.Windows.Forms.Label lblMqttPassword;
        private System.Windows.Forms.TextBox txtMqttPassword;
        private System.Windows.Forms.CheckBox chkMqttSslTls;
        private System.Windows.Forms.Button btnMqttConectar;
        private System.Windows.Forms.Button btnMqttDesconectar;
        private System.Windows.Forms.GroupBox grpConfigModbus;
        private System.Windows.Forms.ComboBox cmbModbusConfig;
        private System.Windows.Forms.TextBox txtModbusConfigNombre;
        private System.Windows.Forms.Button btnModbusGuardarConfig;
        private System.Windows.Forms.Button btnModbusNuevaConfig;
        private System.Windows.Forms.DataGridView dgvModbusResultados;
        private System.Windows.Forms.GroupBox grpConexionModbus;
        private System.Windows.Forms.Label lblModbusIp;
        private System.Windows.Forms.TextBox txtModbusIp;
        private System.Windows.Forms.Label lblModbusPuerto;
        private System.Windows.Forms.NumericUpDown nudModbusPuerto;
        private System.Windows.Forms.Label lblModbusDeviceId;
        private System.Windows.Forms.NumericUpDown nudModbusDeviceId;
        private System.Windows.Forms.Button btnModbusConectar;
        private System.Windows.Forms.Button btnModbusDesconectar;
        private System.Windows.Forms.GroupBox grpOperacionesModbus;
        private System.Windows.Forms.Label lblModbusDireccionRegistro;
        private System.Windows.Forms.TextBox txtModbusDireccionRegistro;
        private System.Windows.Forms.Label lblModbusValorEscritura;
        private System.Windows.Forms.TextBox txtModbusValorEscritura;
        private System.Windows.Forms.Button btnModbusLeerRegistros;
        private System.Windows.Forms.Button btnModbusEscribirRegistro;
        private System.Windows.Forms.GroupBox grpDatosSinoptico;
        private System.Windows.Forms.TextBox txtSinopticoFiltro;
        private System.Windows.Forms.Button btnSinopticoActualizar;
        private System.Windows.Forms.DataGridView dgvSinopticoDatos;
        private System.Windows.Forms.Label lblTopicosActivos;
        private System.Windows.Forms.Label lblUltimaActualizacion;
        private System.Windows.Forms.Timer timer1;
    }
}
