using System.Net.Sockets;
using NModbus;

namespace ControlDeProducciónENAGAS.Clases
{
    /// <summary>
    /// Clase para conexión y operaciones Modbus TCP
    /// </summary>
    public class ConexionModbus : IDisposable
    {
        #region Propiedades Privadas
        private string _ip = "*************";
        private int _puerto = 502;
        private byte _deviceId = 1;
        private int _timeout = 5000;
        private bool _estaConectado = false;
        private TcpClient? _tcpClient;
        private IModbusMaster? _modbusMaster;
        private readonly object _lockObject = new object();
        #endregion

        #region Propiedades Públicas
        /// <summary>
        /// Dirección IP del PLC Modbus
        /// </summary>
        public string Ip
        {
            get => _ip;
            set => _ip = value ?? "*************";
        }

        /// <summary>
        /// Puerto TCP del PLC Modbus (por defecto 502)
        /// </summary>
        public int Puerto
        {
            get => _puerto;
            set => _puerto = value > 0 ? value : 502;
        }

        /// <summary>
        /// Device ID del PLC (Slave ID)
        /// </summary>
        public byte DeviceId
        {
            get => _deviceId;
            set => _deviceId = value;
        }

        /// <summary>
        /// Timeout de conexión en milisegundos
        /// </summary>
        public int Timeout
        {
            get => _timeout;
            set => _timeout = value > 0 ? value : 5000;
        }

        /// <summary>
        /// Indica si está conectado al PLC
        /// </summary>
        public bool EstaConectado => _estaConectado && _tcpClient?.Connected == true;
        #endregion

        #region Eventos
        /// <summary>
        /// Evento que se dispara cuando se conecta
        /// </summary>
        public event EventHandler? Conectado;

        /// <summary>
        /// Evento que se dispara cuando se desconecta
        /// </summary>
        public event EventHandler? Desconectado;

        /// <summary>
        /// Evento que se dispara cuando ocurre un error
        /// </summary>
        public event EventHandler<string>? ErrorOcurrido;
        #endregion

        #region Constructor y Destructor
        /// <summary>
        /// Constructor por defecto
        /// </summary>
        public ConexionModbus()
        {
        }

        /// <summary>
        /// Constructor con parámetros
        /// </summary>
        public ConexionModbus(string ip, int puerto = 502, byte deviceId = 1, int timeout = 5000)
        {
            Ip = ip;
            Puerto = puerto;
            DeviceId = deviceId;
            Timeout = timeout;
        }

        /// <summary>
        /// Destructor
        /// </summary>
        ~ConexionModbus()
        {
            Dispose(false);
        }
        #endregion

        #region Métodos de Conexión
        /// <summary>
        /// Conecta al PLC Modbus TCP
        /// </summary>
        /// <returns>True si la conexión fue exitosa</returns>
        public async Task<bool> ConectarAsync()
        {
            try
            {
                if (EstaConectado) return true;

                lock (_lockObject)
                {
                    // Crear cliente TCP
                    _tcpClient = new TcpClient();
                    _tcpClient.ReceiveTimeout = Timeout;
                    _tcpClient.SendTimeout = Timeout;
                }

                // Conectar de forma asíncrona
                await _tcpClient.ConnectAsync(Ip, Puerto);

                lock (_lockObject)
                {
                    // Crear factory y master Modbus
                    var factory = new ModbusFactory();
                    _modbusMaster = factory.CreateMaster(_tcpClient);
                    _modbusMaster.Transport.Retries = 3;
                    _modbusMaster.Transport.WaitToRetryMilliseconds = 250;
                    _estaConectado = true;
                }

                // Disparar evento
                Conectado?.Invoke(this, EventArgs.Empty);
                return true;
            }
            catch (Exception ex)
            {
                Desconectar();
                ErrorOcurrido?.Invoke(this, $"Error conectando a Modbus: {ex.Message}");
                throw new Exception($"❌ Error conectando a Modbus TCP {Ip}:{Puerto} - {ex.Message}");
            }
        }

        /// <summary>
        /// Desconecta del PLC Modbus
        /// </summary>
        public void Desconectar()
        {
            try
            {
                lock (_lockObject)
                {
                    _estaConectado = false;

                    _modbusMaster?.Dispose();
                    _modbusMaster = null;

                    _tcpClient?.Close();
                    _tcpClient?.Dispose();
                    _tcpClient = null;
                }

                // Disparar evento
                Desconectado?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ErrorOcurrido?.Invoke(this, $"Error desconectando Modbus: {ex.Message}");
            }
        }
        #endregion

        #region Métodos de Lectura
        /// <summary>
        /// Lee registros de holding (función 03)
        /// </summary>
        /// <param name="direccionInicio">Dirección inicial</param>
        /// <param name="cantidad">Cantidad de registros a leer</param>
        /// <returns>Array de valores leídos</returns>
        public async Task<ushort[]> LeerHoldingRegistersAsync(ushort direccionInicio, ushort cantidad)
        {
            if (!EstaConectado)
                throw new Exception("❌ No hay conexión Modbus activa");

            try
            {
                return await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        return _modbusMaster!.ReadHoldingRegisters(DeviceId, direccionInicio, cantidad);
                    }
                });
            }
            catch (Exception ex)
            {
                ErrorOcurrido?.Invoke(this, $"Error leyendo holding registers: {ex.Message}");
                throw new Exception($"❌ Error leyendo registros {direccionInicio}-{direccionInicio + cantidad - 1}: {ex.Message}");
            }
        }

        /// <summary>
        /// Lee registros de entrada (función 04)
        /// </summary>
        /// <param name="direccionInicio">Dirección inicial</param>
        /// <param name="cantidad">Cantidad de registros a leer</param>
        /// <returns>Array de valores leídos</returns>
        public async Task<ushort[]> LeerInputRegistersAsync(ushort direccionInicio, ushort cantidad)
        {
            if (!EstaConectado)
                throw new Exception("❌ No hay conexión Modbus activa");

            try
            {
                return await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        return _modbusMaster!.ReadInputRegisters(DeviceId, direccionInicio, cantidad);
                    }
                });
            }
            catch (Exception ex)
            {
                ErrorOcurrido?.Invoke(this, $"Error leyendo input registers: {ex.Message}");
                throw new Exception($"❌ Error leyendo registros de entrada {direccionInicio}-{direccionInicio + cantidad - 1}: {ex.Message}");
            }
        }

        /// <summary>
        /// Lee coils (función 01)
        /// </summary>
        /// <param name="direccionInicio">Dirección inicial</param>
        /// <param name="cantidad">Cantidad de coils a leer</param>
        /// <returns>Array de valores booleanos</returns>
        public async Task<bool[]> LeerCoilsAsync(ushort direccionInicio, ushort cantidad)
        {
            if (!EstaConectado)
                throw new Exception("❌ No hay conexión Modbus activa");

            try
            {
                return await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        return _modbusMaster!.ReadCoils(DeviceId, direccionInicio, cantidad);
                    }
                });
            }
            catch (Exception ex)
            {
                ErrorOcurrido?.Invoke(this, $"Error leyendo coils: {ex.Message}");
                throw new Exception($"❌ Error leyendo coils {direccionInicio}-{direccionInicio + cantidad - 1}: {ex.Message}");
            }
        }
        #endregion

        #region Métodos de Escritura
        /// <summary>
        /// Escribe un registro de holding (función 06)
        /// </summary>
        /// <param name="direccion">Dirección del registro</param>
        /// <param name="valor">Valor a escribir</param>
        public async Task EscribirHoldingRegisterAsync(ushort direccion, ushort valor)
        {
            if (!EstaConectado)
                throw new Exception("❌ No hay conexión Modbus activa");

            try
            {
                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        _modbusMaster!.WriteSingleRegister(DeviceId, direccion, valor);
                    }
                });
            }
            catch (Exception ex)
            {
                ErrorOcurrido?.Invoke(this, $"Error escribiendo holding register: {ex.Message}");
                throw new Exception($"❌ Error escribiendo registro {direccion}: {ex.Message}");
            }
        }

        /// <summary>
        /// Escribe un coil (función 05)
        /// </summary>
        /// <param name="direccion">Dirección del coil</param>
        /// <param name="valor">Valor a escribir</param>
        public async Task EscribirCoilAsync(ushort direccion, bool valor)
        {
            if (!EstaConectado)
                throw new Exception("❌ No hay conexión Modbus activa");

            try
            {
                await Task.Run(() =>
                {
                    lock (_lockObject)
                    {
                        _modbusMaster!.WriteSingleCoil(DeviceId, direccion, valor);
                    }
                });
            }
            catch (Exception ex)
            {
                ErrorOcurrido?.Invoke(this, $"Error escribiendo coil: {ex.Message}");
                throw new Exception($"❌ Error escribiendo coil {direccion}: {ex.Message}");
            }
        }
        #endregion

        #region IDisposable
        /// <summary>
        /// Libera recursos
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Libera recursos
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                Desconectar();
            }
        }
        #endregion

        #region Métodos de Utilidad
        /// <summary>
        /// Representación en string de la conexión
        /// </summary>
        public override string ToString()
        {
            string estado = EstaConectado ? "Conectado" : "Desconectado";
            return $"Modbus TCP {Ip}:{Puerto} (Device {DeviceId}) - {estado}";
        }
        #endregion
    }
}
