{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\wetransfer_parajorgr_2025-06-12_1126\\parajorgr\\pruebaCore\\pruebaCore.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\wetransfer_parajorgr_2025-06-12_1126\\parajorgr\\pruebaCore\\pruebaCore.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\wetransfer_parajorgr_2025-06-12_1126\\parajorgr\\pruebaCore\\pruebaCore.csproj", "projectName": "pruebaCore", "projectPath": "C:\\Users\\<USER>\\Downloads\\wetransfer_parajorgr_2025-06-12_1126\\parajorgr\\pruebaCore\\pruebaCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\wetransfer_parajorgr_2025-06-12_1126\\parajorgr\\pruebaCore\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"BouncyCastle.Cryptography": {"target": "Package", "version": "[2.5.1, )"}, "MQTTnet": {"target": "Package", "version": "[4.3.7.1207, )"}, "MailKit": {"target": "Package", "version": "[4.12.0, )"}, "Microsoft.CSharp": {"target": "Package", "version": "[4.7.0, )"}, "Microsoft.Exchange.WebServices": {"target": "Package", "version": "[2.2.0, )"}, "MimeKit": {"target": "Package", "version": "[4.12.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Data.DataSetExtensions": {"target": "Package", "version": "[4.5.0, )"}, "System.Formats.Asn1": {"target": "Package", "version": "[9.0.5, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.5, )"}, "System.Runtime.CompilerServices.Unsafe": {"target": "Package", "version": "[6.1.2, )"}, "System.Runtime.InteropServices.RuntimeInformation": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}