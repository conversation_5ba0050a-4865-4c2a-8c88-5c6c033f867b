{
    // Configuración para depuración de proyectos .NET en Cursor/VS Code
    "version": "0.2.0",
    "configurations": [
        {
            "name": ".NET Core Launch (console)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/bin/Debug/net9.0-windows/pruebaCore.dll",
            "args": [],
            "cwd": "${workspaceFolder}",
            "console": "internalConsole",
            "stopAtEntry": false,
            "env": {
                "DOTNET_ENVIRONMENT": "Development"
            }
        },
        {
            "name": ".NET Core Attach",
            "type": "coreclr",
            "request": "attach"
        },
        {
            "name": "netcoredbg (custom)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/bin/Debug/net9.0-windows/pruebaCore.dll",
            "args": [],
            "cwd": "${workspaceFolder}",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "powershell",
                "pipeArgs": ["-Command"],
                "debuggerPath": "C:\\Users\\<USER>\\Downloads\\netcoredbg-win64\\netcoredbg.exe",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "DOTNET_ENVIRONMENT": "Development"
            }
        }
    ]
}
