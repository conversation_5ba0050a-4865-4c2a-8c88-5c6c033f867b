using System;
using System.Windows.Forms;
using ControlDeProducciónENAGAS.Formularios;

namespace ControlDeProducciónENAGAS.Clases
{
    /// <summary>
    /// Clase helper para facilitar la integración entre el formulario MAIN 
    /// y la configuración MQTT
    /// </summary>
    public static class MqttFormHelper
    {
        /// <summary>
        /// Extrae la configuración MQTT desde los controles del formulario MAIN
        /// </summary>
        /// <param name="formulario">Instancia del formulario MAIN</param>
        /// <returns>Configuración MQTT extraída</returns>
        public static MqttConfiguracion ExtraerConfiguracionDesdeFormulario(MAIN formulario)
        {
            if (formulario == null)
                throw new ArgumentNullException(nameof(formulario));

            var config = new MqttConfiguracion();

            try
            {
                // Buscar los controles por nombre usando reflexión
                var tipoFormulario = formulario.GetType();

                // Protocolo
                var cmbProtocolo = BuscarControl<ComboBox>(formulario, "cmbMqttProtocolo");
                if (cmbProtocolo?.SelectedItem != null)
                    config.Protocolo = cmbProtocolo.SelectedItem.ToString() ?? "mqtt://";

                // Host
                var txtHost = BuscarControl<TextBox>(formulario, "txtMqttHost");
                if (txtHost != null)
                    config.Host = txtHost.Text;

                // Puerto
                var txtPuerto = BuscarControl<TextBox>(formulario, "txtMqttPuerto");
                if (txtPuerto != null && int.TryParse(txtPuerto.Text, out int puerto))
                    config.Puerto = puerto;

                // Client ID
                var txtClientId = BuscarControl<TextBox>(formulario, "txtMqttClientId");
                if (txtClientId != null)
                    config.ClientId = txtClientId.Text;

                // Usuario
                var txtUsuario = BuscarControl<TextBox>(formulario, "txtMqttUsuario");
                if (txtUsuario != null)
                    config.Usuario = txtUsuario.Text;

                // Password
                var txtPassword = BuscarControl<TextBox>(formulario, "txtMqttPassword");
                if (txtPassword != null)
                    config.Password = txtPassword.Text;

                // SSL/TLS
                var chkSslTls = BuscarControl<CheckBox>(formulario, "chkMqttSslTls");
                if (chkSslTls != null)
                    config.UsarSslTls = chkSslTls.Checked;

                return config;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error al extraer configuración MQTT: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Aplica una configuración MQTT a los controles del formulario MAIN
        /// </summary>
        /// <param name="formulario">Instancia del formulario MAIN</param>
        /// <param name="config">Configuración a aplicar</param>
        public static void AplicarConfiguracionAlFormulario(MAIN formulario, MqttConfiguracion config)
        {
            if (formulario == null)
                throw new ArgumentNullException(nameof(formulario));
            if (config == null)
                throw new ArgumentNullException(nameof(config));

            try
            {
                // Protocolo
                var cmbProtocolo = BuscarControl<ComboBox>(formulario, "cmbMqttProtocolo");
                if (cmbProtocolo != null)
                {
                    cmbProtocolo.SelectedItem = config.Protocolo;
                    if (cmbProtocolo.SelectedItem == null && cmbProtocolo.Items.Contains(config.Protocolo))
                        cmbProtocolo.SelectedItem = config.Protocolo;
                }

                // Host
                var txtHost = BuscarControl<TextBox>(formulario, "txtMqttHost");
                if (txtHost != null)
                    txtHost.Text = config.Host;

                // Puerto
                var txtPuerto = BuscarControl<TextBox>(formulario, "txtMqttPuerto");
                if (txtPuerto != null)
                    txtPuerto.Text = config.Puerto.ToString();

                // Client ID
                var txtClientId = BuscarControl<TextBox>(formulario, "txtMqttClientId");
                if (txtClientId != null)
                    txtClientId.Text = config.ClientId;

                // Usuario
                var txtUsuario = BuscarControl<TextBox>(formulario, "txtMqttUsuario");
                if (txtUsuario != null)
                    txtUsuario.Text = config.Usuario;

                // Password
                var txtPassword = BuscarControl<TextBox>(formulario, "txtMqttPassword");
                if (txtPassword != null)
                    txtPassword.Text = config.Password;

                // SSL/TLS
                var chkSslTls = BuscarControl<CheckBox>(formulario, "chkMqttSslTls");
                if (chkSslTls != null)
                    chkSslTls.Checked = config.UsarSslTls;

                // Actualizar estado de conexión
                ActualizarEstadoConexion(formulario, config);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error al aplicar configuración MQTT: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Actualiza el estado de conexión en el formulario
        /// </summary>
        /// <param name="formulario">Instancia del formulario MAIN</param>
        /// <param name="config">Configuración con el estado actual</param>
        public static void ActualizarEstadoConexion(MAIN formulario, MqttConfiguracion config)
        {
            if (formulario == null || config == null) return;

            try
            {
                // Actualizar label de estado
                var lblEstado = BuscarControl<Label>(formulario, "lblEstadoMqtt");
                if (lblEstado != null)
                    lblEstado.Text = config.EstadoTexto;

                // Actualizar botones
                var btnConectar = BuscarControl<Button>(formulario, "btnMqttConectar");
                var btnDesconectar = BuscarControl<Button>(formulario, "btnMqttDesconectar");

                if (btnConectar != null)
                    btnConectar.Enabled = !config.EstaConectado && config.EsConfiguracionValida();

                if (btnDesconectar != null)
                    btnDesconectar.Enabled = config.EstaConectado;

                // Cambiar color del panel de estado
                var panelEstado = BuscarControl<Panel>(formulario, "panelEstadoMqtt");
                if (panelEstado != null)
                {
                    panelEstado.BackColor = config.EstaConectado 
                        ? System.Drawing.Color.Green 
                        : System.Drawing.Color.Gray;
                }
            }
            catch (Exception ex)
            {
                // Log del error pero no lanzar excepción para no interrumpir la UI
                System.Diagnostics.Debug.WriteLine($"Error actualizando estado MQTT: {ex.Message}");
            }
        }

        /// <summary>
        /// Valida la configuración y muestra errores si los hay
        /// </summary>
        /// <param name="config">Configuración a validar</param>
        /// <param name="mostrarMensaje">Si debe mostrar mensaje de error</param>
        /// <returns>True si la configuración es válida</returns>
        public static bool ValidarConfiguracion(MqttConfiguracion config, bool mostrarMensaje = true)
        {
            if (config == null) return false;

            var errores = config.ObtenerErroresValidacion();
            
            if (errores.Count > 0 && mostrarMensaje)
            {
                var mensaje = "⚠️ Errores en la configuración MQTT:\n\n" + string.Join("\n", errores);
                MessageBox.Show(mensaje, "Configuración MQTT", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }

            return errores.Count == 0;
        }

        /// <summary>
        /// Busca un control específico en el formulario por su nombre
        /// </summary>
        /// <typeparam name="T">Tipo del control</typeparam>
        /// <param name="contenedor">Contenedor donde buscar</param>
        /// <param name="nombre">Nombre del control</param>
        /// <returns>Control encontrado o null</returns>
        private static T? BuscarControl<T>(Control contenedor, string nombre) where T : Control
        {
            if (contenedor == null || string.IsNullOrEmpty(nombre))
                return null;

            // Buscar directamente por nombre
            var controles = contenedor.Controls.Find(nombre, true);
            if (controles.Length > 0 && controles[0] is T control)
                return control;

            // Buscar recursivamente
            foreach (Control hijo in contenedor.Controls)
            {
                var encontrado = BuscarControl<T>(hijo, nombre);
                if (encontrado != null)
                    return encontrado;
            }

            return null;
        }

        /// <summary>
        /// Crea una configuración MQTT con valores por defecto para ENAGAS
        /// </summary>
        /// <returns>Configuración con valores típicos</returns>
        public static MqttConfiguracion CrearConfiguracionPorDefecto()
        {
            return new MqttConfiguracion
            {
                Protocolo = "mqtt://",
                Host = "localhost",
                Puerto = 1883,
                Usuario = "",
                Password = "",
                UsarSslTls = false
            };
        }

        /// <summary>
        /// Exporta la configuración a un string JSON simple
        /// </summary>
        /// <param name="config">Configuración a exportar</param>
        /// <returns>String JSON con la configuración</returns>
        public static string ExportarConfiguracion(MqttConfiguracion config)
        {
            if (config == null) return "{}";

            return $@"{{
    ""protocolo"": ""{config.Protocolo}"",
    ""host"": ""{config.Host}"",
    ""puerto"": {config.Puerto},
    ""clientId"": ""{config.ClientId}"",
    ""usuario"": ""{config.Usuario}"",
    ""usarSslTls"": {config.UsarSslTls.ToString().ToLower()}
}}";
        }
    }
}
