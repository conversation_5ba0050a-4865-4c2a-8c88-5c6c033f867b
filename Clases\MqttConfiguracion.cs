using System;
using System.ComponentModel;

namespace ControlDeProducciónENAGAS.Clases
{
    /// <summary>
    /// Clase que encapsula todos los parámetros de configuración MQTT
    /// del panel MQTT del formulario MAIN
    /// </summary>
    public class MqttConfiguracion : INotifyPropertyChanged
    {
        #region Propiedades Privadas
        private string _protocolo = "mqtt://";
        private string _host = "";
        private int _puerto = 1883;
        private string _clientId = "";
        private string _usuario = "";
        private string _password = "";
        private bool _usarSslTls = false;
        private bool _estaConectado = false;
        #endregion

        #region Propiedades Públicas
        /// <summary>
        /// Protocolo MQTT (mqtt://, mqtts://, ws://, wss://)
        /// </summary>
        public string Protocolo
        {
            get => _protocolo;
            set
            {
                if (_protocolo != value)
                {
                    _protocolo = value;
                    OnPropertyChanged(nameof(Protocolo));
                    
                    // Ajustar puerto por defecto según protocolo
                    if (value == "mqtt://" || value == "ws://")
                        Puerto = 1883;
                    else if (value == "mqtts://" || value == "wss://")
                        Puerto = 8883;
                }
            }
        }

        /// <summary>
        /// Dirección del servidor MQTT
        /// </summary>
        public string Host
        {
            get => _host;
            set
            {
                if (_host != value)
                {
                    _host = value;
                    OnPropertyChanged(nameof(Host));
                }
            }
        }

        /// <summary>
        /// Puerto del servidor MQTT
        /// </summary>
        public int Puerto
        {
            get => _puerto;
            set
            {
                if (_puerto != value && value > 0 && value <= 65535)
                {
                    _puerto = value;
                    OnPropertyChanged(nameof(Puerto));
                }
            }
        }

        /// <summary>
        /// ID único del cliente MQTT
        /// </summary>
        public string ClientId
        {
            get => _clientId;
            set
            {
                if (_clientId != value)
                {
                    _clientId = value;
                    OnPropertyChanged(nameof(ClientId));
                }
            }
        }

        /// <summary>
        /// Usuario para autenticación MQTT
        /// </summary>
        public string Usuario
        {
            get => _usuario;
            set
            {
                if (_usuario != value)
                {
                    _usuario = value;
                    OnPropertyChanged(nameof(Usuario));
                }
            }
        }

        /// <summary>
        /// Contraseña para autenticación MQTT
        /// </summary>
        public string Password
        {
            get => _password;
            set
            {
                if (_password != value)
                {
                    _password = value;
                    OnPropertyChanged(nameof(Password));
                }
            }
        }

        /// <summary>
        /// Indica si se debe usar SSL/TLS para la conexión
        /// </summary>
        public bool UsarSslTls
        {
            get => _usarSslTls;
            set
            {
                if (_usarSslTls != value)
                {
                    _usarSslTls = value;
                    OnPropertyChanged(nameof(UsarSslTls));
                }
            }
        }

        /// <summary>
        /// Estado actual de la conexión MQTT
        /// </summary>
        public bool EstaConectado
        {
            get => _estaConectado;
            set
            {
                if (_estaConectado != value)
                {
                    _estaConectado = value;
                    OnPropertyChanged(nameof(EstaConectado));
                    OnPropertyChanged(nameof(EstadoTexto));
                }
            }
        }

        /// <summary>
        /// Texto descriptivo del estado de conexión
        /// </summary>
        public string EstadoTexto => EstaConectado ? "MQTT: Conectado ✅" : "MQTT: Desconectado ❌";

        /// <summary>
        /// URL completa de conexión MQTT
        /// </summary>
        public string UrlCompleta => $"{Protocolo}{Host}:{Puerto}";
        #endregion

        #region Constructores
        /// <summary>
        /// Constructor por defecto
        /// </summary>
        public MqttConfiguracion()
        {
            GenerarClientIdAleatorio();
        }

        /// <summary>
        /// Constructor con parámetros básicos
        /// </summary>
        /// <param name="host">Dirección del servidor</param>
        /// <param name="puerto">Puerto del servidor</param>
        /// <param name="protocolo">Protocolo a usar</param>
        public MqttConfiguracion(string host, int puerto, string protocolo = "mqtt://")
        {
            Host = host;
            Puerto = puerto;
            Protocolo = protocolo;
            GenerarClientIdAleatorio();
        }

        /// <summary>
        /// Constructor completo
        /// </summary>
        public MqttConfiguracion(string protocolo, string host, int puerto, string clientId, 
                                string usuario, string password, bool usarSslTls)
        {
            Protocolo = protocolo;
            Host = host;
            Puerto = puerto;
            ClientId = clientId;
            Usuario = usuario;
            Password = password;
            UsarSslTls = usarSslTls;
        }
        #endregion

        #region Métodos Públicos
        /// <summary>
        /// Genera un Client ID aleatorio único
        /// </summary>
        public void GenerarClientIdAleatorio()
        {
            ClientId = $"ENAGAS_Client_{Guid.NewGuid().ToString("N")[..8]}";
        }

        /// <summary>
        /// Valida que la configuración sea válida para conectar
        /// </summary>
        /// <returns>True si la configuración es válida</returns>
        public bool EsConfiguracionValida()
        {
            return !string.IsNullOrWhiteSpace(Host) &&
                   Puerto > 0 && Puerto <= 65535 &&
                   !string.IsNullOrWhiteSpace(ClientId) &&
                   !string.IsNullOrWhiteSpace(Protocolo);
        }

        /// <summary>
        /// Obtiene los errores de validación
        /// </summary>
        /// <returns>Lista de errores encontrados</returns>
        public List<string> ObtenerErroresValidacion()
        {
            var errores = new List<string>();

            if (string.IsNullOrWhiteSpace(Host))
                errores.Add("❌ El host no puede estar vacío");

            if (Puerto <= 0 || Puerto > 65535)
                errores.Add("❌ El puerto debe estar entre 1 y 65535");

            if (string.IsNullOrWhiteSpace(ClientId))
                errores.Add("❌ El Client ID no puede estar vacío");

            if (string.IsNullOrWhiteSpace(Protocolo))
                errores.Add("❌ Debe seleccionar un protocolo");

            return errores;
        }

        /// <summary>
        /// Crea una copia de la configuración actual
        /// </summary>
        /// <returns>Nueva instancia con los mismos valores</returns>
        public MqttConfiguracion Clonar()
        {
            return new MqttConfiguracion(Protocolo, Host, Puerto, ClientId, Usuario, Password, UsarSslTls)
            {
                EstaConectado = this.EstaConectado
            };
        }

        /// <summary>
        /// Resetea la configuración a valores por defecto
        /// </summary>
        public void Resetear()
        {
            Protocolo = "mqtt://";
            Host = "";
            Puerto = 1883;
            Usuario = "";
            Password = "";
            UsarSslTls = false;
            EstaConectado = false;
            GenerarClientIdAleatorio();
        }

        /// <summary>
        /// Representación en string de la configuración
        /// </summary>
        public override string ToString()
        {
            return $"MQTT Config: {UrlCompleta} | Client: {ClientId} | Estado: {EstadoTexto}";
        }
        #endregion

        #region INotifyPropertyChanged
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion
    }
}
