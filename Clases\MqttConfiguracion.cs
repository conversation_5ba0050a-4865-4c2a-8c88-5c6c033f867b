using System;
using System.Collections.Generic;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;

namespace ControlDeProducciónENAGAS.Clases
{
    /// <summary>
    /// Clase para conexión y operaciones MQTT con los parámetros del formulario MAIN
    /// </summary>
    public class ConexionMQTT
    {
        #region Propiedades Privadas
        private string _protocolo = "mqtt://";
        private string _host = "";
        private int _puerto = 1883;
        private string _clientId = "";
        private string _usuario = "";
        private string _password = "";
        private bool _usarSslTls = false;
        private bool _estaConectado = false;
        private TcpClient? _tcpClient;
        private NetworkStream? _stream;
        private Dictionary<string, string> _topicValues = new Dictionary<string, string>();
        #endregion

        #region Propiedades Públicas
        /// <summary>
        /// Protocolo MQTT (mqtt://, mqtts://, ws://, wss://)
        /// </summary>
        public string Protocolo
        {
            get => _protocolo;
            set
            {
                if (_protocolo != value)
                {
                    _protocolo = value;

                    // Ajustar puerto por defecto según protocolo
                    if (value == "mqtt://" || value == "ws://")
                        Puerto = 1883;
                    else if (value == "mqtts://" || value == "wss://")
                        Puerto = 8883;
                }
            }
        }

        /// <summary>
        /// Dirección del servidor MQTT
        /// </summary>
        public string Host
        {
            get => _host;
            set => _host = value;
        }

        /// <summary>
        /// Puerto del servidor MQTT
        /// </summary>
        public int Puerto
        {
            get => _puerto;
            set
            {
                if (value > 0 && value <= 65535)
                    _puerto = value;
            }
        }

        /// <summary>
        /// ID único del cliente MQTT
        /// </summary>
        public string ClientId
        {
            get => _clientId;
            set => _clientId = value;
        }

        /// <summary>
        /// Usuario para autenticación MQTT
        /// </summary>
        public string Usuario
        {
            get => _usuario;
            set => _usuario = value;
        }

        /// <summary>
        /// Contraseña para autenticación MQTT
        /// </summary>
        public string Password
        {
            get => _password;
            set => _password = value;
        }

        /// <summary>
        /// Indica si se debe usar SSL/TLS para la conexión
        /// </summary>
        public bool UsarSslTls
        {
            get => _usarSslTls;
            set => _usarSslTls = value;
        }

        /// <summary>
        /// Estado actual de la conexión MQTT
        /// </summary>
        public bool EstaConectado
        {
            get => _estaConectado;
            set => _estaConectado = value;
        }

        /// <summary>
        /// Texto descriptivo del estado de conexión
        /// </summary>
        public string EstadoTexto => EstaConectado ? "MQTT: Conectado ✅" : "MQTT: Desconectado ❌";

        /// <summary>
        /// URL completa de conexión MQTT
        /// </summary>
        public string UrlCompleta => $"{Protocolo}{Host}:{Puerto}";
        #endregion

        #region Constructores
        /// <summary>
        /// Constructor por defecto
        /// </summary>
        public ConexionMQTT()
        {
            GenerarClientIdAleatorio();
        }

        /// <summary>
        /// Constructor con parámetros básicos
        /// </summary>
        /// <param name="host">Dirección del servidor</param>
        /// <param name="puerto">Puerto del servidor</param>
        /// <param name="protocolo">Protocolo a usar</param>
        public ConexionMQTT(string host, int puerto, string protocolo = "mqtt://")
        {
            Host = host;
            Puerto = puerto;
            Protocolo = protocolo;
            GenerarClientIdAleatorio();
        }

        /// <summary>
        /// Constructor completo
        /// </summary>
        public ConexionMQTT(string protocolo, string host, int puerto, string clientId,
                                string usuario, string password, bool usarSslTls)
        {
            Protocolo = protocolo;
            Host = host;
            Puerto = puerto;
            ClientId = clientId;
            Usuario = usuario;
            Password = password;
            UsarSslTls = usarSslTls;
        }
        #endregion

        #region Métodos Públicos
        /// <summary>
        /// Genera un Client ID aleatorio único
        /// </summary>
        public void GenerarClientIdAleatorio()
        {
            ClientId = $"ENAGAS_Client_{Guid.NewGuid().ToString("N")[..8]}";
        }

        /// <summary>
        /// Conecta al broker MQTT
        /// </summary>
        /// <returns>True si la conexión fue exitosa</returns>
        public async Task<bool> ConectarAsync()
        {
            try
            {
                if (EstaConectado) return true;

                if (!EsConfiguracionValida())
                    throw new InvalidOperationException("❌ Configuración MQTT no válida");

                _tcpClient = new TcpClient();
                await _tcpClient.ConnectAsync(Host, Puerto);
                _stream = _tcpClient.GetStream();

                // Enviar paquete CONNECT MQTT básico
                var connectPacket = CrearPaqueteConnect();
                await _stream.WriteAsync(connectPacket, 0, connectPacket.Length);

                // Leer respuesta CONNACK
                var response = new byte[4];
                await _stream.ReadAsync(response, 0, 4);

                if (response[3] == 0) // Connection Accepted
                {
                    EstaConectado = true;
                    _ = Task.Run(EscucharMensajes); // Iniciar escucha en background
                    return true;
                }
                else
                {
                    Desconectar();
                    return false;
                }
            }
            catch (Exception ex)
            {
                Desconectar();
                throw new Exception($"❌ Error conectando a MQTT: {ex.Message}");
            }
        }

        /// <summary>
        /// Desconecta del broker MQTT
        /// </summary>
        public void Desconectar()
        {
            try
            {
                _stream?.Close();
                _tcpClient?.Close();
                EstaConectado = false;
            }
            catch { }
            finally
            {
                _stream = null;
                _tcpClient = null;
            }
        }

        /// <summary>
        /// Publica un mensaje en un topic específico
        /// </summary>
        /// <param name="topic">Topic donde publicar</param>
        /// <param name="mensaje">Mensaje a publicar</param>
        /// <returns>True si se publicó correctamente</returns>
        public async Task<bool> EscribirTopic(string topic, string mensaje)
        {
            try
            {
                if (!EstaConectado || _stream == null)
                    throw new InvalidOperationException("❌ No hay conexión MQTT activa");

                if (string.IsNullOrWhiteSpace(topic))
                    throw new ArgumentException("❌ El topic no puede estar vacío");

                var publishPacket = CrearPaquetePublish(topic, mensaje);
                await _stream.WriteAsync(publishPacket, 0, publishPacket.Length);

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"❌ Error publicando en topic '{topic}': {ex.Message}");
            }
        }

        /// <summary>
        /// Lee el último valor recibido de un topic
        /// </summary>
        /// <param name="topic">Topic a leer</param>
        /// <returns>Último valor del topic o null si no existe</returns>
        public string? LeerTopic(string topic)
        {
            if (string.IsNullOrWhiteSpace(topic))
                return null;

            return _topicValues.TryGetValue(topic, out string? value) ? value : null;
        }

        /// <summary>
        /// Suscribe a un topic para recibir sus mensajes
        /// </summary>
        /// <param name="topic">Topic al que suscribirse</param>
        /// <returns>True si la suscripción fue exitosa</returns>
        public async Task<bool> SuscribirTopic(string topic)
        {
            try
            {
                if (!EstaConectado || _stream == null)
                    throw new InvalidOperationException("❌ No hay conexión MQTT activa");

                if (string.IsNullOrWhiteSpace(topic))
                    throw new ArgumentException("❌ El topic no puede estar vacío");

                var subscribePacket = CrearPaqueteSuscripcion(topic);
                await _stream.WriteAsync(subscribePacket, 0, subscribePacket.Length);

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"❌ Error suscribiendo a topic '{topic}': {ex.Message}");
            }
        }

        /// <summary>
        /// Valida que la configuración sea válida para conectar
        /// </summary>
        /// <returns>True si la configuración es válida</returns>
        public bool EsConfiguracionValida()
        {
            return !string.IsNullOrWhiteSpace(Host) &&
                   Puerto > 0 && Puerto <= 65535 &&
                   !string.IsNullOrWhiteSpace(ClientId) &&
                   !string.IsNullOrWhiteSpace(Protocolo);
        }

        /// <summary>
        /// Representación en string de la configuración
        /// </summary>
        public override string ToString()
        {
            return $"MQTT: {UrlCompleta} | Client: {ClientId} | Estado: {EstadoTexto}";
        }
        #endregion

        #region Métodos Privados
        /// <summary>
        /// Crea el paquete CONNECT para MQTT
        /// </summary>
        private byte[] CrearPaqueteConnect()
        {
            var clientIdBytes = Encoding.UTF8.GetBytes(ClientId);
            var userBytes = string.IsNullOrEmpty(Usuario) ? new byte[0] : Encoding.UTF8.GetBytes(Usuario);
            var passBytes = string.IsNullOrEmpty(Password) ? new byte[0] : Encoding.UTF8.GetBytes(Password);

            var payload = new List<byte>();

            // Protocol Name "MQTT"
            payload.AddRange(new byte[] { 0x00, 0x04 });
            payload.AddRange(Encoding.UTF8.GetBytes("MQTT"));

            // Protocol Level (4 for MQTT 3.1.1)
            payload.Add(0x04);

            // Connect Flags
            byte connectFlags = 0x02; // Clean Session
            if (!string.IsNullOrEmpty(Usuario))
            {
                connectFlags |= 0x80; // Username flag
                if (!string.IsNullOrEmpty(Password))
                    connectFlags |= 0x40; // Password flag
            }
            payload.Add(connectFlags);

            // Keep Alive (60 seconds)
            payload.AddRange(new byte[] { 0x00, 0x3C });

            // Client ID
            payload.AddRange(new byte[] { (byte)(clientIdBytes.Length >> 8), (byte)(clientIdBytes.Length & 0xFF) });
            payload.AddRange(clientIdBytes);

            // Username
            if (userBytes.Length > 0)
            {
                payload.AddRange(new byte[] { (byte)(userBytes.Length >> 8), (byte)(userBytes.Length & 0xFF) });
                payload.AddRange(userBytes);
            }

            // Password
            if (passBytes.Length > 0)
            {
                payload.AddRange(new byte[] { (byte)(passBytes.Length >> 8), (byte)(passBytes.Length & 0xFF) });
                payload.AddRange(passBytes);
            }

            // Fixed Header
            var packet = new List<byte> { 0x10 }; // CONNECT packet type
            packet.Add((byte)payload.Count); // Remaining length
            packet.AddRange(payload);

            return packet.ToArray();
        }

        /// <summary>
        /// Crea el paquete PUBLISH para MQTT
        /// </summary>
        private byte[] CrearPaquetePublish(string topic, string mensaje)
        {
            var topicBytes = Encoding.UTF8.GetBytes(topic);
            var messageBytes = Encoding.UTF8.GetBytes(mensaje);

            var payload = new List<byte>();

            // Topic Length + Topic
            payload.AddRange(new byte[] { (byte)(topicBytes.Length >> 8), (byte)(topicBytes.Length & 0xFF) });
            payload.AddRange(topicBytes);

            // Message
            payload.AddRange(messageBytes);

            // Fixed Header
            var packet = new List<byte> { 0x30 }; // PUBLISH packet type, QoS 0
            packet.Add((byte)payload.Count); // Remaining length
            packet.AddRange(payload);

            return packet.ToArray();
        }

        /// <summary>
        /// Crea el paquete SUBSCRIBE para MQTT
        /// </summary>
        private byte[] CrearPaqueteSuscripcion(string topic)
        {
            var topicBytes = Encoding.UTF8.GetBytes(topic);

            var payload = new List<byte>();

            // Packet Identifier
            payload.AddRange(new byte[] { 0x00, 0x01 });

            // Topic Length + Topic
            payload.AddRange(new byte[] { (byte)(topicBytes.Length >> 8), (byte)(topicBytes.Length & 0xFF) });
            payload.AddRange(topicBytes);

            // QoS
            payload.Add(0x00);

            // Fixed Header
            var packet = new List<byte> { 0x82 }; // SUBSCRIBE packet type
            packet.Add((byte)payload.Count); // Remaining length
            packet.AddRange(payload);

            return packet.ToArray();
        }

        /// <summary>
        /// Escucha mensajes MQTT en background
        /// </summary>
        private async Task EscucharMensajes()
        {
            var buffer = new byte[1024];

            try
            {
                while (EstaConectado && _stream != null)
                {
                    var bytesRead = await _stream.ReadAsync(buffer, 0, buffer.Length);
                    if (bytesRead > 0)
                    {
                        ProcesarMensajeRecibido(buffer, bytesRead);
                    }
                    await Task.Delay(10); // Pequeña pausa para no saturar CPU
                }
            }
            catch (Exception)
            {
                // Conexión perdida
                EstaConectado = false;
            }
        }

        /// <summary>
        /// Procesa mensajes MQTT recibidos
        /// </summary>
        private void ProcesarMensajeRecibido(byte[] buffer, int length)
        {
            try
            {
                if (length < 2) return;

                byte messageType = (byte)((buffer[0] & 0xF0) >> 4);

                // PUBLISH message (tipo 3)
                if (messageType == 3)
                {
                    int pos = 2; // Saltar fixed header

                    // Leer topic length
                    if (pos + 1 >= length) return;
                    int topicLength = (buffer[pos] << 8) | buffer[pos + 1];
                    pos += 2;

                    // Leer topic
                    if (pos + topicLength >= length) return;
                    string topic = Encoding.UTF8.GetString(buffer, pos, topicLength);
                    pos += topicLength;

                    // Leer mensaje
                    int messageLength = length - pos;
                    if (messageLength > 0)
                    {
                        string mensaje = Encoding.UTF8.GetString(buffer, pos, messageLength);
                        _topicValues[topic] = mensaje;
                    }
                }
            }
            catch (Exception)
            {
                // Ignorar errores de parsing
            }
        }
        #endregion
    }
}
