using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using MQTTnet;
using MQTTnet.Client;

namespace ControlDeProducciónENAGAS.Clases
{
    /// <summary>
    /// Clase para conexión y operaciones MQTT con los parámetros del formulario MAIN
    /// </summary>
    public class ConexionMQTT
    {
        #region Propiedades Privadas
        private string _protocolo = "mqtt://";
        private string _host = "";
        private int _puerto = 1883;
        private string _clientId = "";
        private string _usuario = "";
        private string _password = "";
        private bool _usarSslTls = false;
        private bool _estaConectado = false;
        private IMqttClient? _mqttClient;
        private Dictionary<string, string> _topicValues = new Dictionary<string, string>();
        #endregion

        #region Propiedades Públicas
        /// <summary>
        /// Protocolo MQTT (mqtt://, mqtts://, ws://, wss://)
        /// </summary>
        public string Protocolo
        {
            get => _protocolo;
            set
            {
                if (_protocolo != value)
                {
                    _protocolo = value;

                    // Ajustar puerto por defecto según protocolo
                    if (value == "mqtt://" || value == "ws://")
                        Puerto = 1883;
                    else if (value == "mqtts://" || value == "wss://")
                        Puerto = 8883;
                }
            }
        }

        /// <summary>
        /// Dirección del servidor MQTT
        /// </summary>
        public string Host
        {
            get => _host;
            set => _host = value;
        }

        /// <summary>
        /// Puerto del servidor MQTT
        /// </summary>
        public int Puerto
        {
            get => _puerto;
            set
            {
                if (value > 0 && value <= 65535)
                    _puerto = value;
            }
        }

        /// <summary>
        /// ID único del cliente MQTT
        /// </summary>
        public string ClientId
        {
            get => _clientId;
            set => _clientId = value;
        }

        /// <summary>
        /// Usuario para autenticación MQTT
        /// </summary>
        public string Usuario
        {
            get => _usuario;
            set => _usuario = value;
        }

        /// <summary>
        /// Contraseña para autenticación MQTT
        /// </summary>
        public string Password
        {
            get => _password;
            set => _password = value;
        }

        /// <summary>
        /// Indica si se debe usar SSL/TLS para la conexión
        /// </summary>
        public bool UsarSslTls
        {
            get => _usarSslTls;
            set => _usarSslTls = value;
        }

        /// <summary>
        /// Estado actual de la conexión MQTT
        /// </summary>
        public bool EstaConectado
        {
            get => _estaConectado;
            set => _estaConectado = value;
        }

        /// <summary>
        /// Texto descriptivo del estado de conexión
        /// </summary>
        public string EstadoTexto => EstaConectado ? "MQTT: Conectado ✅" : "MQTT: Desconectado ❌";

        /// <summary>
        /// URL completa de conexión MQTT
        /// </summary>
        public string UrlCompleta => $"{Protocolo}{Host}:{Puerto}";
        #endregion

        #region Constructores
        /// <summary>
        /// Constructor por defecto
        /// </summary>
        public ConexionMQTT()
        {
            GenerarClientIdAleatorio();
        }

        /// <summary>
        /// Constructor con parámetros básicos
        /// </summary>
        /// <param name="host">Dirección del servidor</param>
        /// <param name="puerto">Puerto del servidor</param>
        /// <param name="protocolo">Protocolo a usar</param>
        public ConexionMQTT(string host, int puerto, string protocolo = "mqtt://")
        {
            Host = host;
            Puerto = puerto;
            Protocolo = protocolo;
            GenerarClientIdAleatorio();
        }

        /// <summary>
        /// Constructor completo
        /// </summary>
        public ConexionMQTT(string protocolo, string host, int puerto, string clientId,
                                string usuario, string password, bool usarSslTls)
        {
            Protocolo = protocolo;
            Host = host;
            Puerto = puerto;
            ClientId = clientId;
            Usuario = usuario;
            Password = password;
            UsarSslTls = usarSslTls;
        }
        #endregion

        #region Métodos Públicos
        /// <summary>
        /// Genera un Client ID aleatorio único
        /// </summary>
        public void GenerarClientIdAleatorio()
        {
            ClientId = $"ENAGAS_Client_{Guid.NewGuid().ToString("N")[..8]}";
        }

        /// <summary>
        /// Conecta al broker MQTT usando MQTTnet
        /// </summary>
        /// <returns>True si la conexión fue exitosa</returns>
        public async Task<bool> ConectarAsync()
        {
            try
            {
                if (EstaConectado) return true;

                if (!EsConfiguracionValida())
                    throw new InvalidOperationException("❌ Configuración MQTT no válida");

                // Crear cliente MQTT
                var factory = new MqttFactory();
                _mqttClient = factory.CreateMqttClient();

                // Configurar opciones de conexión
                var optionsBuilder = new MqttClientOptionsBuilder()
                    .WithClientId(ClientId)
                    .WithTcpServer(Host, Puerto)
                    .WithCleanSession(true)
                    .WithKeepAlivePeriod(TimeSpan.FromSeconds(60))
                    .WithTimeout(TimeSpan.FromSeconds(30));

                // Agregar credenciales si están configuradas
                if (!string.IsNullOrEmpty(Usuario) && !string.IsNullOrEmpty(Password))
                {
                    optionsBuilder.WithCredentials(Usuario, Password);
                }

                // Configurar SSL/TLS si está habilitado
                if (UsarSslTls)
                {
                    optionsBuilder.WithTls(new MqttClientOptionsBuilderTlsParameters
                    {
                        UseTls = true,
                        AllowUntrustedCertificates = true,
                        IgnoreCertificateChainErrors = true,
                        IgnoreCertificateRevocationErrors = true,
                        CertificateValidationHandler = _ => true
                    });
                }

                var options = optionsBuilder.Build();

                // Configurar eventos
                _mqttClient.ApplicationMessageReceivedAsync += OnMessageReceived;
                _mqttClient.DisconnectedAsync += OnDisconnected;

                // Conectar con timeout
                var connectTask = _mqttClient.ConnectAsync(options);
                var result = await connectTask.ConfigureAwait(false);

                if (result.ResultCode == MqttClientConnectResultCode.Success)
                {
                    EstaConectado = true;
                    return true;
                }
                else
                {
                    string errorDetail = $"Código: {result.ResultCode}";
                    if (!string.IsNullOrEmpty(result.ReasonString))
                        errorDetail += $", Razón: {result.ReasonString}";
                    if (!string.IsNullOrEmpty(result.UserProperties?.ToString()))
                        errorDetail += $", Propiedades: {result.UserProperties}";

                    throw new Exception($"Conexión fallida - {errorDetail}");
                }
            }
            catch (Exception ex)
            {
                Desconectar();
                throw new Exception($"❌ Error conectando a MQTT: {ex.Message}");
            }
        }

        /// <summary>
        /// Desconecta del broker MQTT
        /// </summary>
        public void Desconectar()
        {
            try
            {
                if (_mqttClient?.IsConnected == true)
                {
                    _mqttClient.DisconnectAsync().Wait(5000);
                }
                _mqttClient?.Dispose();
                EstaConectado = false;
            }
            catch { }
            finally
            {
                _mqttClient = null;
            }
        }

        /// <summary>
        /// Publica un mensaje en un topic específico
        /// </summary>
        /// <param name="topic">Topic donde publicar</param>
        /// <param name="mensaje">Mensaje a publicar</param>
        /// <returns>True si se publicó correctamente</returns>
        public async Task<bool> EscribirTopic(string topic, string mensaje)
        {
            try
            {
                if (!EstaConectado || _mqttClient == null)
                    throw new InvalidOperationException("❌ No hay conexión MQTT activa");

                if (string.IsNullOrWhiteSpace(topic))
                    throw new ArgumentException("❌ El topic no puede estar vacío");

                var message = new MqttApplicationMessageBuilder()
                    .WithTopic(topic)
                    .WithPayload(mensaje)
                    .WithQualityOfServiceLevel(MQTTnet.Protocol.MqttQualityOfServiceLevel.AtMostOnce)
                    .Build();

                await _mqttClient.PublishAsync(message);
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"❌ Error publicando en topic '{topic}': {ex.Message}");
            }
        }

        /// <summary>
        /// Lee el último valor recibido de un topic
        /// </summary>
        /// <param name="topic">Topic a leer</param>
        /// <returns>Último valor del topic o null si no existe</returns>
        public string? LeerTopic(string topic)
        {
            if (string.IsNullOrWhiteSpace(topic))
                return null;

            return _topicValues.TryGetValue(topic, out string? value) ? value : null;
        }

        /// <summary>
        /// Suscribe a un topic para recibir sus mensajes
        /// </summary>
        /// <param name="topic">Topic al que suscribirse</param>
        /// <returns>True si la suscripción fue exitosa</returns>
        public async Task<bool> SuscribirTopic(string topic)
        {
            try
            {
                if (!EstaConectado || _mqttClient == null)
                    throw new InvalidOperationException("❌ No hay conexión MQTT activa");

                if (string.IsNullOrWhiteSpace(topic))
                    throw new ArgumentException("❌ El topic no puede estar vacío");

                var subscribeOptions = new MqttClientSubscribeOptionsBuilder()
                    .WithTopicFilter(f => f.WithTopic(topic))
                    .Build();

                await _mqttClient.SubscribeAsync(subscribeOptions);
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"❌ Error suscribiendo a topic '{topic}': {ex.Message}");
            }
        }

        /// <summary>
        /// Valida que la configuración sea válida para conectar
        /// </summary>
        /// <returns>True si la configuración es válida</returns>
        public bool EsConfiguracionValida()
        {
            return !string.IsNullOrWhiteSpace(Host) &&
                   Puerto > 0 && Puerto <= 65535 &&
                   !string.IsNullOrWhiteSpace(ClientId) &&
                   !string.IsNullOrWhiteSpace(Protocolo);
        }

        /// <summary>
        /// Representación en string de la configuración
        /// </summary>
        public override string ToString()
        {
            return $"MQTT: {UrlCompleta} | Client: {ClientId} | Estado: {EstadoTexto}";
        }
        #endregion

        #region Métodos Privados
        /// <summary>
        /// Maneja mensajes MQTT recibidos
        /// </summary>
        private Task OnMessageReceived(MqttApplicationMessageReceivedEventArgs e)
        {
            try
            {
                string topic = e.ApplicationMessage.Topic;
                string payload = Encoding.UTF8.GetString(e.ApplicationMessage.Payload ?? new byte[0]);

                _topicValues[topic] = payload;
            }
            catch (Exception ex)
            {
                // Log error but don't throw
                System.Diagnostics.Debug.WriteLine($"Error procesando mensaje MQTT: {ex.Message}");
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Maneja desconexiones MQTT
        /// </summary>
        private Task OnDisconnected(MqttClientDisconnectedEventArgs e)
        {
            EstaConectado = false;
            return Task.CompletedTask;
        }
        #endregion
    }
}
