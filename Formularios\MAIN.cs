﻿using ControlDeProducciónENAGAS.Clases;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ControlDeProducciónENAGAS.Formularios
{
    public partial class MAIN : Form
    {


        public MAIN()
        {
            InitializeComponent();

            // Configura el estado inicial del formulario
            ConfigurarEstadoInicial();

            // Cargar configuración MQTT automáticamente
            CargarConfiguracionMQTT();

            // Inicializar DataGridView de topics MQTT
            InicializarDataGridViewTopics();
        }

        private ConexionMQTT? mqtt;
        private bool MQTTConectado = false;

        // Declaración del DataGridView para topics MQTT (agregar desde diseñador)
        private DataGridView? dgvMqttTopics;


        // GESTIÓN DE LA NAVEGACIÓN Y DISEÑO ***************************************************************

        /// <summary>
        /// Método central para cambiar de pestaña y actualizar el estilo de los botones.
        /// </summary>
        /// <param name="paginaDestino">La TabPage que se va a mostrar.</param>
        /// <param name="botonActivo">El botón de navegación que se va a resaltar.</param>
        /// 
        private void ConfigurarEstadoInicial()
        {
            // Opcional: Oculta las cabeceras de las pestañas para que el usuario solo navegue con los botones.
            // Si quieres ver las pestañas, comenta o elimina las siguientes 3 líneas.
            tabControlPrincipal.Appearance = TabAppearance.FlatButtons;
            tabControlPrincipal.ItemSize = new Size(0, 1);
            tabControlPrincipal.SizeMode = TabSizeMode.Fixed;

            // Establece la pestaña de Correo como la inicial
            NavegarHacia(tabPageCorreo, btnNavCorreo);
        }
        private void NavegarHacia(TabPage paginaDestino, Button botonActivo)
        {
            // Cambia la pestaña activa en el TabControl
            if (tabControlPrincipal.SelectedTab != paginaDestino)
            {
                tabControlPrincipal.SelectedTab = paginaDestino;
            }

            // Actualiza el estilo de los botones
            ResetearEstilosBotones();
            ResaltarBoton(botonActivo);
        }

        /// <summary>
        /// Pone todos los botones de navegación en su estado "inactivo".
        /// </summary>
        private void ResetearEstilosBotones()
        {
            Color colorInactivo = Color.FromArgb(60, 63, 65);
            Font fuenteNormal = new Font("Segoe UI", 12F, FontStyle.Regular);

            btnNavCorreo.BackColor = colorInactivo;
            btnNavCorreo.Font = fuenteNormal;
            btnNavMqtt.BackColor = colorInactivo;
            btnNavMqtt.Font = fuenteNormal;
            btnNavModbus.BackColor = colorInactivo;
            btnNavModbus.Font = fuenteNormal;
            btnNavSinoptico.BackColor = colorInactivo;
            btnNavSinoptico.Font = fuenteNormal;
        }

        /// <summary>
        /// Resalta un botón de navegación para mostrar que está "activo".
        /// </summary>
        private void ResaltarBoton(Button boton)
        {
            boton.BackColor = Color.FromArgb(0, 122, 204);
            boton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
        }


        // --- EVENTOS CLICK DE LOS BOTONES DE NAVEGACIÓN ---

        private void btnNavCorreo_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageCorreo, btnNavCorreo);
        }

        private void btnNavMqtt_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageMqtt, btnNavMqtt);
        }

        private void btnNavModbus_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageModbus, btnNavModbus);
        }

        private void btnNavSinoptico_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageSinoptico, btnNavSinoptico);
        }
        private void btnCerrar_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        private void btnLimpiarLog_Click(object sender, EventArgs e)
        {
            rtbLog.Clear();
            rtbLog.AppendText("=== LOG DEL SISTEMA (limpiado) ===\n");
        }




        private async void btnMqttConectar_Click(object sender, EventArgs e)
        {
            try
            {
                // Validar puerto
                if (!int.TryParse(txtMqttPuerto.Text.Trim(), out int port))
                {
                    MessageBox.Show("El puerto MQTT debe ser un número válido.", "Puerto Inválido", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Puerto MQTT inválido\n");
                    return;
                }

                // Crear conexión con datos del formulario
                mqtt = new ConexionMQTT();

                // USAR LA IMPLEMENTACIÓN EXACTA DEL EJEMPLO FUNCIONAL
                bool conectado = await mqtt.ConectarAsync(
                    cmbMqttProtocolo.SelectedItem?.ToString() ?? "mqtts://",  // protocol
                    txtMqttHost.Text.Trim(),                                  // hostAddress
                    port,                                                     // port
                    txtMqttClientId.Text.Trim(),                             // clientId
                    txtMqttUsuario.Text.Trim(),                              // username
                    txtMqttPassword.Text,                                     // password (SIN Trim!)
                    chkMqttSslTls.Checked,                                   // useTlsFromCheckbox
                    true,                                                     // isCASigned (CA signed certificate)
                    false                                                     // useAlpn
                );

                if (conectado)
                {
                    lblEstadoMqtt.Text = "MQTT: Conectado ✅";
                    panelEstadoMqtt.BackColor = Color.Green;
                    btnMqttConectar.Enabled = false;
                    btnMqttDesconectar.Enabled = true;

                    // Suscribir topics del CSV
                    SuscribirTopicsDelCSV();

                    // Habilitar Lectura de Topics
                    MQTTConectado = true;

                    // Actualizar lista de topics
                    ActualizarListaTopics();

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Conectado a MQTT: {txtMqttHost.Text}:{port}\n");
                    rtbLog.ScrollToCaret();
                }
                else
                {
                    MessageBox.Show("No se pudo conectar al broker MQTT");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error conectando a MQTT\n");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error al conectar MQTT: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error conectando a MQTT: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }


        private async void SuscribirTopicsDelCSV()
        {
            try
            {
                if (!File.Exists("topics.csv"))
                {
                    File.WriteAllText("topics.csv", "enagas/temperatura\nenagas/presion\nenagas/estado\nenagas/caudal");
                }

                string[] topics = File.ReadAllLines("topics.csv");

                foreach (string topic in topics)
                {
                    if (!topic.StartsWith("#") && !string.IsNullOrEmpty(topic.Trim()))
                    {
                        await mqtt.SuscribirTopic(topic.Trim());
                    }
                }
            }
            catch
            {
                // Ignorar errores
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            if (mqtt == null || !mqtt.EstaConectado) return;

            try
            {
                // Actualizar lista de topics en DataGridView
                ActualizarListaTopics();

                // Leer archivo CSV y mostrar en log (opcional)
                if (File.Exists("topics.csv"))
                {
                    string[] topics = File.ReadAllLines("topics.csv");

                    foreach (string topic in topics)
                    {
                        if (!topic.StartsWith("#") && !string.IsNullOrEmpty(topic.Trim()))
                        {
                            string? valor = mqtt.LeerTopic(topic.Trim());
                            if (valor != null)
                            {
                                // Solo mostrar en log si hay cambios (opcional)
                                // rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} {topic}: {valor}\n");
                                // rtbLog.ScrollToCaret();
                            }
                        }
                    }
                }
            }
            catch
            {
                // Ignorar errores de lectura
            }
        }

        private void btnMqttDesconectar_Click(object sender, EventArgs e)
        {
            mqtt?.Desconectar();

            lblEstadoMqtt.Text = "MQTT: Desconectado ❌";
            panelEstadoMqtt.BackColor = Color.Gray;
            btnMqttConectar.Enabled = true;
            btnMqttDesconectar.Enabled = false;
            MQTTConectado = false;

            // Limpiar lista de topics
            if (dgvMqttTopics != null)
            {
                dgvMqttTopics.DataSource = null;
            }
        }

        /// <summary>
        /// Carga la configuración MQTT desde el archivo JSON y la aplica al formulario
        /// </summary>
        private void CargarConfiguracionMQTT()
        {
            try
            {
                var config = ConfiguracionMQTT.CargarDesdeArchivo();

                // Aplicar configuración a los controles del formulario
                cmbMqttProtocolo.SelectedItem = config.Protocolo;
                txtMqttHost.Text = config.Host;
                txtMqttPuerto.Text = config.Puerto.ToString();
                txtMqttClientId.Text = config.ClientId;
                txtMqttUsuario.Text = config.Usuario;
                txtMqttPassword.Text = config.Password;
                chkMqttSslTls.Checked = config.UsarSslTls;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚙️ Configuración MQTT cargada: {config.Name}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🌐 Servidor: {config.Host}:{config.Puerto}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔒 SSL/TLS: {(config.UsarSslTls ? "Activado" : "Desactivado")}\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error cargando configuración: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }




        /// <summary>
        /// Inicializa el DataGridView para mostrar los topics MQTT (respeta el diseño del Designer)
        /// </summary>
        private void InicializarDataGridViewTopics()
        {
            try
            {
                // Verificar que el control existe (debe ser agregado desde el diseñador)
                if (dgvMqttTopics == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ DataGridView dgvMqttTopics no encontrado\n");
                    return;
                }

                // Solo configurar si no hay columnas (respeta el diseño del Designer)
                if (dgvMqttTopics.Columns.Count == 0)
                {
                    dgvMqttTopics.AutoGenerateColumns = false;

                    // Columna Topic
                    var colTopic = new DataGridViewTextBoxColumn
                    {
                        Name = "Topic",
                        HeaderText = "📡 Topic",
                        DataPropertyName = "Topic",
                        Width = 300,
                        ReadOnly = true
                    };
                    dgvMqttTopics.Columns.Add(colTopic);

                    // Columna Valor
                    var colValor = new DataGridViewTextBoxColumn
                    {
                        Name = "Valor",
                        HeaderText = "📊 Valor",
                        DataPropertyName = "Valor",
                        Width = 200,
                        ReadOnly = true
                    };
                    dgvMqttTopics.Columns.Add(colValor);

                    // Columna Timestamp
                    var colTimestamp = new DataGridViewTextBoxColumn
                    {
                        Name = "Timestamp",
                        HeaderText = "🕒 Última Actualización",
                        DataPropertyName = "Timestamp",
                        Width = 150,
                        ReadOnly = true
                    };
                    dgvMqttTopics.Columns.Add(colTimestamp);

                    // Columna Estado
                    var colEstado = new DataGridViewTextBoxColumn
                    {
                        Name = "Estado",
                        HeaderText = "🔄 Estado",
                        DataPropertyName = "Estado",
                        Width = 100,
                        ReadOnly = true
                    };
                    dgvMqttTopics.Columns.Add(colEstado);

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Columnas de DataGridView creadas desde código\n");
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ DataGridView usa columnas del Designer\n");
                }

                // Configurar propiedades básicas (no afecta al Designer)
                dgvMqttTopics.AutoGenerateColumns = false;
                dgvMqttTopics.AllowUserToAddRows = false;
                dgvMqttTopics.AllowUserToDeleteRows = false;
                dgvMqttTopics.ReadOnly = true;
                dgvMqttTopics.SelectionMode = DataGridViewSelectionMode.FullRowSelect;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ DataGridView de topics MQTT inicializado\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error inicializando DataGridView: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza la lista de topics en el DataGridView
        /// </summary>
        private void ActualizarListaTopics()
        {
            try
            {
                if (dgvMqttTopics == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ dgvMqttTopics es null\n");
                    return;
                }

                if (mqtt == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ mqtt es null\n");
                    return;
                }

                if (!mqtt.EstaConectado)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ MQTT no está conectado\n");
                    return;
                }

                // Obtener todos los topics con sus valores
                var topicsData = mqtt.ObtenerTodosLosTopics();
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 Topics encontrados: {topicsData.Count}\n");

                // Crear lista para el DataGridView
                var listaTopics = new List<object>();

                foreach (var topic in topicsData)
                {
                    listaTopics.Add(new
                    {
                        Topic = topic.Key,
                        Valor = topic.Value ?? "Sin datos",
                        Timestamp = DateTime.Now.ToString("HH:mm:ss"),
                        Estado = string.IsNullOrEmpty(topic.Value) ? "⏳ Esperando" : "✅ Activo"
                    });
                }

                // Actualizar DataGridView en el hilo de UI
                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        dgvMqttTopics.DataSource = null;
                        dgvMqttTopics.DataSource = listaTopics;
                        dgvMqttTopics.Refresh();
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ DataGridView actualizado con {listaTopics.Count} topics\n");
                    }));
                }
                else
                {
                    dgvMqttTopics.DataSource = null;
                    dgvMqttTopics.DataSource = listaTopics;
                    dgvMqttTopics.Refresh();
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ DataGridView actualizado con {listaTopics.Count} topics\n");
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error actualizando topics: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        private void btnMqttDesconectar_Click_1(object sender, EventArgs e)
        {

        }

        /// <summary>
        /// Método de prueba para cargar topics de ejemplo (para depuración)
        /// </summary>
        public void CargarTopicsDeEjemplo()
        {
            try
            {
                if (dgvMqttTopics == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ dgvMqttTopics no existe\n");
                    return;
                }

                // Crear datos de ejemplo
                var topicsEjemplo = new List<object>
                {
                    new { Topic = "enagas/temperatura", Valor = "25.5°C", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "✅ Activo" },
                    new { Topic = "enagas/presion", Valor = "1.2 bar", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "✅ Activo" },
                    new { Topic = "enagas/estado", Valor = "Sin datos", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "⏳ Esperando" },
                    new { Topic = "enagas/caudal", Valor = "150 m³/h", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "✅ Activo" }
                };

                dgvMqttTopics.DataSource = topicsEjemplo;
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Topics de ejemplo cargados\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error cargando topics de ejemplo: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        // FIN GESTIÓN DE LA NAVEGACIÓN ************************************************************************
    }
}
