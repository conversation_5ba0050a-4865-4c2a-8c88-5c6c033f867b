﻿using ControlDeProducciónENAGAS.Clases;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ControlDeProducciónENAGAS.Formularios
{
    public partial class MAIN : Form
    {


        public MAIN()
        {
            InitializeComponent();

            // Configura el estado inicial del formulario
            ConfigurarEstadoInicial();

            // Cargar configuración MQTT automáticamente
            CargarConfiguracionMQTT();

            // Inicializar DataGridView de topics MQTT
            InicializarDataGridViewTopics();

            // Inicializar botón de refrescar MQTT
            InicializarBotonRefrescarMQTT();

            // Inicializar sistema sinóptico
            InicializarSistemaSinoptico();

            // Inicializar controles de visualización sinóptico
            InicializarVisualizacionSinoptico();
        }

        private ConexionMQTT? mqtt;
        private bool MQTTConectado = false;

        // Declaración del DataGridView para topics MQTT (agregar desde diseñador)
        private DataGridView? dgvMqttTopics;

        // El botón btnRefrescarMQTT ya está declarado en el Designer

        // Sistema de monitorización bidireccional para sinóptico
        private readonly Dictionary<string, string> topicControlFeedback = new Dictionary<string, string>
        {
            { "DD/ENAGAS/ALMENDRALEJO/CONSIGNAPA", "DD/ENAGAS/ALMENDRALEJO/FEEDBACKPA" },
            { "DD/ENAGAS/ALMENDRALEJO/MOTIVOPA", "DD/ENAGAS/ALMENDRALEJO/FEEDBACKMOTIVOPA" }
        };

        private readonly Dictionary<string, string> ultimosValoresControl = new Dictionary<string, string>();
        private bool sinopticoHabilitado = false;

        // Controles para visualización del sinóptico (creados dinámicamente)
        private DataGridView? dgvSinopticoEstado;
        private Label? lblSinopticoEstado;
        private Label? lblSinopticoUltimaActualizacion;
        private GroupBox? grpSinopticoInfo;


        // GESTIÓN DE LA NAVEGACIÓN Y DISEÑO ***************************************************************

        /// <summary>
        /// Método central para cambiar de pestaña y actualizar el estilo de los botones.
        /// </summary>
        /// <param name="paginaDestino">La TabPage que se va a mostrar.</param>
        /// <param name="botonActivo">El botón de navegación que se va a resaltar.</param>
        /// 
        private void ConfigurarEstadoInicial()
        {
            // Opcional: Oculta las cabeceras de las pestañas para que el usuario solo navegue con los botones.
            // Si quieres ver las pestañas, comenta o elimina las siguientes 3 líneas.
            tabControlPrincipal.Appearance = TabAppearance.FlatButtons;
            tabControlPrincipal.ItemSize = new Size(0, 1);
            tabControlPrincipal.SizeMode = TabSizeMode.Fixed;

            // Establece la pestaña de Correo como la inicial
            NavegarHacia(tabPageCorreo, btnNavCorreo);
        }
        private void NavegarHacia(TabPage paginaDestino, Button botonActivo)
        {
            // Cambia la pestaña activa en el TabControl
            if (tabControlPrincipal.SelectedTab != paginaDestino)
            {
                tabControlPrincipal.SelectedTab = paginaDestino;
            }

            // Actualiza el estilo de los botones
            ResetearEstilosBotones();
            ResaltarBoton(botonActivo);
        }

        /// <summary>
        /// Pone todos los botones de navegación en su estado "inactivo".
        /// </summary>
        private void ResetearEstilosBotones()
        {
            Color colorInactivo = Color.FromArgb(60, 63, 65);
            Font fuenteNormal = new Font("Segoe UI", 12F, FontStyle.Regular);

            btnNavCorreo.BackColor = colorInactivo;
            btnNavCorreo.Font = fuenteNormal;
            btnNavMqtt.BackColor = colorInactivo;
            btnNavMqtt.Font = fuenteNormal;
            btnNavModbus.BackColor = colorInactivo;
            btnNavModbus.Font = fuenteNormal;
            btnNavSinoptico.BackColor = colorInactivo;
            btnNavSinoptico.Font = fuenteNormal;
        }

        /// <summary>
        /// Resalta un botón de navegación para mostrar que está "activo".
        /// </summary>
        private void ResaltarBoton(Button boton)
        {
            boton.BackColor = Color.FromArgb(0, 122, 204);
            boton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
        }


        // --- EVENTOS CLICK DE LOS BOTONES DE NAVEGACIÓN ---

        private void btnNavCorreo_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageCorreo, btnNavCorreo);
        }

        private void btnNavMqtt_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageMqtt, btnNavMqtt);
        }

        private void btnNavModbus_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageModbus, btnNavModbus);
        }

        private void btnNavSinoptico_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageSinoptico, btnNavSinoptico);
        }
        private void btnCerrar_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        private void btnLimpiarLog_Click(object sender, EventArgs e)
        {
            rtbLog.Clear();
            rtbLog.AppendText("=== LOG DEL SISTEMA (limpiado) ===\n");
        }




        private async void btnMqttConectar_Click(object sender, EventArgs e)
        {
            try
            {
                // Validar puerto
                if (!int.TryParse(txtMqttPuerto.Text.Trim(), out int port))
                {
                    MessageBox.Show("El puerto MQTT debe ser un número válido.", "Puerto Inválido", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Puerto MQTT inválido\n");
                    return;
                }

                // Crear conexión con datos del formulario
                mqtt = new ConexionMQTT();

                // USAR LA IMPLEMENTACIÓN EXACTA DEL EJEMPLO FUNCIONAL
                bool conectado = await mqtt.ConectarAsync(
                    cmbMqttProtocolo.SelectedItem?.ToString() ?? "mqtts://",  // protocol
                    txtMqttHost.Text.Trim(),                                  // hostAddress
                    port,                                                     // port
                    txtMqttClientId.Text.Trim(),                             // clientId
                    txtMqttUsuario.Text.Trim(),                              // username
                    txtMqttPassword.Text,                                     // password (SIN Trim!)
                    chkMqttSslTls.Checked,                                   // useTlsFromCheckbox
                    true,                                                     // isCASigned (CA signed certificate)
                    false                                                     // useAlpn
                );

                if (conectado)
                {
                    lblEstadoMqtt.Text = "MQTT: Conectado ✅";
                    panelEstadoMqtt.BackColor = Color.Green;
                    btnMqttConectar.Enabled = false;
                    btnMqttDesconectar.Enabled = true;

                    // Habilitar botón de refrescar MQTT
                    if (btnRefrescarMQTT != null)
                        btnRefrescarMQTT.Enabled = true;

                    // Suscribir topics del CSV
                    SuscribirTopicsDelCSV();

                    // Suscribir topics del sinóptico
                    SuscribirTopicsSinoptico();

                    // Habilitar Lectura de Topics
                    MQTTConectado = true;

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Conectado a MQTT: {txtMqttHost.Text}:{port}\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 💡 Usa el botón 'Refrescar MQTT' para ver los topics disponibles\n");
                    rtbLog.ScrollToCaret();
                }
                else
                {
                    MessageBox.Show("No se pudo conectar al broker MQTT");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error conectando a MQTT\n");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error al conectar MQTT: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error conectando a MQTT: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }


        private async void SuscribirTopicsDelCSV()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 Iniciando suscripción de topics desde CSV...\n");

                if (!File.Exists("topics.csv"))
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ topics.csv no existe, creando archivo de ejemplo...\n");
                    File.WriteAllText("topics.csv", "enagas/temperatura\nenagas/presion\nenagas/estado\nenagas/caudal");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Archivo topics.csv creado\n");
                }

                string[] topics = File.ReadAllLines("topics.csv");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 Leyendo {topics.Length} líneas de topics.csv\n");

                int topicsSuscritos = 0;
                foreach (string topic in topics)
                {
                    if (!topic.StartsWith("#") && !string.IsNullOrEmpty(topic.Trim()))
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📡 Suscribiendo a: {topic.Trim()}\n");
                        await mqtt.SuscribirTopic(topic.Trim());
                        topicsSuscritos++;
                    }
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Suscripción completada: {topicsSuscritos} topics\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error en suscripción de topics: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            // Timer ya no actualiza automáticamente el DataGridView
            // Solo se actualiza manualmente con el botón btnRefrescarMQTT

            // Procesar cambios en topics de control del sinóptico
            ProcesarCambiosTopicsControl();

            // Actualizar visualización del sinóptico
            ActualizarVisualizacionSinoptico();
        }

        private void btnMqttDesconectar_Click(object sender, EventArgs e)
        {
            mqtt?.Desconectar();

            lblEstadoMqtt.Text = "MQTT: Desconectado ❌";
            panelEstadoMqtt.BackColor = Color.Gray;
            btnMqttConectar.Enabled = true;
            btnMqttDesconectar.Enabled = false;
            MQTTConectado = false;

            // Deshabilitar botón de refrescar MQTT
            if (btnRefrescarMQTT != null)
                btnRefrescarMQTT.Enabled = false;

            // Deshabilitar sistema sinóptico
            DeshabilitarSistemaSinoptico();

            // Limpiar lista de topics
            if (dgvMqttTopics != null)
            {
                dgvMqttTopics.DataSource = null;
            }
        }

        /// <summary>
        /// Carga la configuración MQTT desde el archivo JSON y la aplica al formulario
        /// </summary>
        private void CargarConfiguracionMQTT()
        {
            try
            {
                var config = ConfiguracionMQTT.CargarDesdeArchivo();

                // Aplicar configuración a los controles del formulario
                cmbMqttProtocolo.SelectedItem = config.Protocolo;
                txtMqttHost.Text = config.Host;
                txtMqttPuerto.Text = config.Puerto.ToString();
                txtMqttClientId.Text = config.ClientId;
                txtMqttUsuario.Text = config.Usuario;
                txtMqttPassword.Text = config.Password;
                chkMqttSslTls.Checked = config.UsarSslTls;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚙️ Configuración MQTT cargada: {config.Name}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🌐 Servidor: {config.Host}:{config.Puerto}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔒 SSL/TLS: {(config.UsarSslTls ? "Activado" : "Desactivado")}\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error cargando configuración: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }




        /// <summary>
        /// Inicializa el DataGridView para mostrar los topics MQTT (SOLO verifica, NO modifica Designer)
        /// </summary>
        private void InicializarDataGridViewTopics()
        {
            try
            {
                // Verificar que el control existe (debe ser agregado desde el diseñador)
                if (dgvMqttTopics == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CRITICAL: DataGridView dgvMqttTopics not found - must be added in Designer\n");
                    return;
                }

                // SOLO VERIFICAR - NO MODIFICAR NADA DEL DESIGNER
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 DataGridView found - Columns: {dgvMqttTopics.Columns.Count}\n");

                // Listar columnas configuradas en el Designer
                for (int i = 0; i < dgvMqttTopics.Columns.Count; i++)
                {
                    var col = dgvMqttTopics.Columns[i];
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 Column {i}: Name='{col.Name}', DataPropertyName='{col.DataPropertyName}'\n");
                }

                if (dgvMqttTopics.Columns.Count == 0)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ WARNING: No columns configured in Designer\n");
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ DataGridView configured in Designer with {dgvMqttTopics.Columns.Count} columns\n");
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error checking DataGridView: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Inicializa el botón de refrescar MQTT topics
        /// </summary>
        private void InicializarBotonRefrescarMQTT()
        {
            try
            {
                if (btnRefrescarMQTT == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ Button btnRefrescarMQTT not found - must be added in Designer\n");
                    return;
                }

                // Configurar el botón (el evento Click ya está configurado en el Designer)
                btnRefrescarMQTT.Enabled = false; // Inicialmente deshabilitado

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Refresh MQTT button configured\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error configuring refresh button: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Evento del botón de refrescar MQTT topics
        /// </summary>
        private void btnRefrescarMQTT_Click(object? sender, EventArgs e)
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 Manual MQTT refresh requested\n");
                ActualizarListaTopics(verbose: true);
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Manual MQTT refresh completed\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error in manual MQTT refresh: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza la lista de topics en el DataGridView usando DataTable para compatibilidad con Designer
        /// </summary>
        private void ActualizarListaTopics(bool verbose = false)
        {
            try
            {
                // Step 1: Verify DataGridView exists
                if (dgvMqttTopics == null)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ DEBUG: dgvMqttTopics is null - check Designer\n");
                    return;
                }

                // Step 2: Verify MQTT connection
                if (mqtt == null)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ DEBUG: mqtt object is null\n");
                    return;
                }

                if (!mqtt.EstaConectado)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ DEBUG: MQTT not connected\n");
                    return;
                }

                // Step 3: Create DataTable compatible with Designer columns
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("Topic", typeof(string));
                dataTable.Columns.Add("Valor", typeof(string));
                dataTable.Columns.Add("Timestamp", typeof(string));
                dataTable.Columns.Add("Estado", typeof(string));

                // Step 4: Get topics from MQTT storage
                var topicsData = mqtt.ObtenerTodosLosTopics();
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: MQTT storage has {topicsData.Count} topics with values\n");

                // Add topics with values
                foreach (var topic in topicsData)
                {
                    dataTable.Rows.Add(
                        topic.Key,
                        topic.Value ?? "Sin datos",
                        DateTime.Now.ToString("HH:mm:ss"),
                        string.IsNullOrEmpty(topic.Value) ? "⏳ Esperando" : "✅ Activo"
                    );
                }

                // Step 5: Also add subscribed topics from CSV that might not have values yet
                if (File.Exists("topics.csv"))
                {
                    string[] csvTopics = File.ReadAllLines("topics.csv");
                    foreach (string csvTopic in csvTopics)
                    {
                        if (!csvTopic.StartsWith("#") && !string.IsNullOrEmpty(csvTopic.Trim()))
                        {
                            string topicName = csvTopic.Trim();
                            // Only add if not already in the list
                            if (!topicsData.ContainsKey(topicName))
                            {
                                dataTable.Rows.Add(
                                    topicName,
                                    "Sin datos",
                                    DateTime.Now.ToString("HH:mm:ss"),
                                    "⏳ Esperando"
                                );
                            }
                        }
                    }
                }

                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: Total topics to display: {dataTable.Rows.Count}\n");

                // Step 6: Update DataGridView using DataTable
                if (InvokeRequired)
                {
                    Invoke(new Action(() => BindDataTableToGrid(dataTable, verbose)));
                }
                else
                {
                    BindDataTableToGrid(dataTable, verbose);
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ ERROR in ActualizarListaTopics: {ex.Message}\n");
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Stack trace: {ex.StackTrace}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Binds DataTable to DataGridView (compatible with Designer columns)
        /// </summary>
        private void BindDataTableToGrid(DataTable dataTable, bool verbose = false)
        {
            try
            {
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 DEBUG: Starting DataTable binding...\n");

                // Clear current data source
                dgvMqttTopics.DataSource = null;

                // Bind DataTable
                dgvMqttTopics.DataSource = dataTable;

                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ DEBUG: DataTable binding completed\n");
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: DataGridView now has {dgvMqttTopics.Rows.Count} rows\n");

                // Force refresh
                dgvMqttTopics.Refresh();
                dgvMqttTopics.Invalidate();

                if (verbose) rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ ERROR in BindDataTableToGrid: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        private void btnMqttDesconectar_Click_1(object sender, EventArgs e)
        {

        }

        /// <summary>
        /// TEST METHOD: Load sample topics to verify DataGridView configuration
        /// Call this method to test if DataGridView can display data
        /// </summary>
        public void TestDataGridViewWithSampleData()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Starting DataGridView test with sample data\n");

                if (dgvMqttTopics == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: dgvMqttTopics is null\n");
                    return;
                }

                // Create sample data matching the expected structure
                var sampleData = new List<object>
                {
                    new { Topic = "test/topic1", Valor = "Sample Value 1", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "✅ Active" },
                    new { Topic = "test/topic2", Valor = "Sample Value 2", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "✅ Active" },
                    new { Topic = "test/topic3", Valor = "No data", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "⏳ Waiting" }
                };

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Binding {sampleData.Count} sample records\n");

                dgvMqttTopics.DataSource = null;
                dgvMqttTopics.DataSource = sampleData;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: DataGridView now shows {dgvMqttTopics.Rows.Count} rows\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ TEST COMPLETED: Check if data appears in DataGridView\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// TEST METHOD: Check MQTT topics storage
        /// Call this method to verify if MQTT topics are being received and stored
        /// </summary>
        public void TestMqttTopicsStorage()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Checking MQTT topics storage\n");

                if (mqtt == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST: MQTT object is null\n");
                    return;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: MQTT Connected: {mqtt.EstaConectado}\n");

                var topics = mqtt.ObtenerTodosLosTopics();
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Total topics in storage: {topics.Count}\n");

                if (topics.Count == 0)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ TEST: No topics found - check MQTT subscription\n");
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 TEST: Topics in storage:\n");
                    foreach (var topic in topics)
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋   - '{topic.Key}' = '{topic.Value}'\n");
                    }
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        #region Sistema Sinóptico MQTT Bidireccional

        /// <summary>
        /// Inicializa el sistema de monitorización bidireccional del sinóptico
        /// </summary>
        private void InicializarSistemaSinoptico()
        {
            try
            {
                // Inicializar diccionario de últimos valores
                foreach (var topicControl in topicControlFeedback.Keys)
                {
                    ultimosValoresControl[topicControl] = string.Empty;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Sistema sinóptico inicializado\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error inicializando sistema sinóptico: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Suscribe a todos los topics del sinóptico (control y feedback)
        /// </summary>
        private async void SuscribirTopicsSinoptico()
        {
            try
            {
                if (mqtt == null || !mqtt.EstaConectado)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: MQTT no conectado\n");
                    return;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 SINÓPTICO: Suscribiendo a topics...\n");

                // Suscribir a topics de control
                foreach (var topicControl in topicControlFeedback.Keys)
                {
                    await mqtt.SuscribirTopic(topicControl);
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📡 SINÓPTICO: Suscrito a control '{topicControl}'\n");
                }

                // Suscribir a topics de feedback
                foreach (var topicFeedback in topicControlFeedback.Values)
                {
                    await mqtt.SuscribirTopic(topicFeedback);
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📡 SINÓPTICO: Suscrito a feedback '{topicFeedback}'\n");
                }

                sinopticoHabilitado = true;
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Sistema habilitado y suscripciones completadas\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error en suscripciones: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Deshabilita el sistema sinóptico
        /// </summary>
        private void DeshabilitarSistemaSinoptico()
        {
            try
            {
                sinopticoHabilitado = false;
                ultimosValoresControl.Clear();

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: Sistema deshabilitado\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error deshabilitando: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Inicializa los controles de visualización del sinóptico DINÁMICAMENTE
        /// </summary>
        private void InicializarVisualizacionSinoptico()
        {
            try
            {
                // Buscar directamente el TabPage del sinóptico
                TabPage? tabSinoptico = null;

                // Buscar por nombre específico primero
                foreach (Control control in tabControlPrincipal.Controls)
                {
                    if (control is TabPage tab && (tab.Name == "tabPageSinoptico" || tab.Text.Contains("SINÓPTICO")))
                    {
                        tabSinoptico = tab;
                        break;
                    }
                }

                if (tabSinoptico == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: No se encontró la pestaña SINÓPTICO\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 SINÓPTICO: Pestañas disponibles:\n");
                    foreach (TabPage tab in tabControlPrincipal.TabPages)
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 - {tab.Name}: '{tab.Text}'\n");
                    }
                    return;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Encontrada pestaña '{tabSinoptico.Text}'\n");

                // Crear GroupBox para información del sinóptico - POSICIONADO EN LA PARTE SUPERIOR VISIBLE
                grpSinopticoInfo = new GroupBox
                {
                    Text = "🔄 Estado del Sistema Sinóptico MQTT",
                    Location = new Point(10, 10), // POSICIÓN SUPERIOR VISIBLE
                    Size = new Size(tabSinoptico.Width - 30, 300), // Tamaño más compacto
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 10, FontStyle.Bold),
                    Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right,
                    BackColor = Color.FromArgb(55, 55, 58)
                };

                // Crear Label de estado
                lblSinopticoEstado = new Label
                {
                    Text = "🔴 Sistema Sinóptico: DESHABILITADO",
                    Location = new Point(15, 25),
                    Size = new Size(400, 25),
                    ForeColor = Color.Yellow,
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    AutoSize = false
                };

                // Crear Label de última actualización
                lblSinopticoUltimaActualizacion = new Label
                {
                    Text = "Última Actualización: --",
                    Location = new Point(420, 25),
                    Size = new Size(300, 25),
                    ForeColor = Color.LightGray,
                    Font = new Font("Segoe UI", 10),
                    AutoSize = false
                };

                // Crear DataGridView para estado de topics
                dgvSinopticoEstado = new DataGridView
                {
                    Location = new Point(15, 60),
                    Size = new Size(grpSinopticoInfo.Width - 30, 270),
                    BackgroundColor = Color.FromArgb(45, 45, 48),
                    ForeColor = Color.White,
                    GridColor = Color.Gray,
                    BorderStyle = BorderStyle.FixedSingle,
                    AllowUserToAddRows = false,
                    AllowUserToDeleteRows = false,
                    AllowUserToResizeRows = false,
                    ReadOnly = true,
                    SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                    AutoGenerateColumns = false,
                    ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                    RowHeadersVisible = false,
                    Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
                };

                // Configurar estilo del DataGridView
                dgvSinopticoEstado.DefaultCellStyle.BackColor = Color.FromArgb(37, 37, 38);
                dgvSinopticoEstado.DefaultCellStyle.ForeColor = Color.White;
                dgvSinopticoEstado.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 122, 204);
                dgvSinopticoEstado.DefaultCellStyle.SelectionForeColor = Color.White;
                dgvSinopticoEstado.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(45, 45, 48);
                dgvSinopticoEstado.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
                dgvSinopticoEstado.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9, FontStyle.Bold);

                // Crear columnas
                dgvSinopticoEstado.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Tipo",
                    HeaderText = "Tipo",
                    DataPropertyName = "Tipo",
                    Width = 100
                });

                dgvSinopticoEstado.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Topic",
                    HeaderText = "Topic",
                    DataPropertyName = "Topic",
                    Width = 200
                });

                dgvSinopticoEstado.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Valor",
                    HeaderText = "Valor",
                    DataPropertyName = "Valor",
                    Width = 120
                });

                dgvSinopticoEstado.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Timestamp",
                    HeaderText = "Timestamp",
                    DataPropertyName = "Timestamp",
                    Width = 100
                });

                dgvSinopticoEstado.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Estado",
                    HeaderText = "Estado",
                    DataPropertyName = "Estado",
                    Width = 120
                });

                dgvSinopticoEstado.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Sincronizado",
                    HeaderText = "Sincronizado",
                    DataPropertyName = "Sincronizado",
                    Width = 120
                });

                // Agregar controles al GroupBox
                grpSinopticoInfo.Controls.Add(lblSinopticoEstado);
                grpSinopticoInfo.Controls.Add(lblSinopticoUltimaActualizacion);
                grpSinopticoInfo.Controls.Add(dgvSinopticoEstado);

                // Agregar GroupBox al panel sinóptico
                tabSinoptico.Controls.Add(grpSinopticoInfo);

                // Forzar actualización visual
                grpSinopticoInfo.BringToFront();
                tabSinoptico.Refresh();

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Controles visuales creados dinámicamente\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📍 SINÓPTICO: GroupBox agregado en posición SUPERIOR VISIBLE (10, 10)\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📏 SINÓPTICO: Tamaño del GroupBox: {grpSinopticoInfo.Size}\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error creando visualización sinóptico: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza la visualización del estado del sinóptico
        /// </summary>
        private void ActualizarVisualizacionSinoptico()
        {
            try
            {
                if (!sinopticoHabilitado || mqtt == null || !mqtt.EstaConectado)
                {
                    // Sistema deshabilitado
                    if (lblSinopticoEstado != null)
                        lblSinopticoEstado.Text = "🔴 Sistema Sinóptico: DESHABILITADO";

                    if (dgvSinopticoEstado != null)
                        dgvSinopticoEstado.DataSource = null;

                    return;
                }

                // Sistema habilitado - actualizar estado
                if (lblSinopticoEstado != null)
                    lblSinopticoEstado.Text = "🟢 Sistema Sinóptico: ACTIVO";

                if (lblSinopticoUltimaActualizacion != null)
                    lblSinopticoUltimaActualizacion.Text = $"Última Actualización: {DateTime.Now:HH:mm:ss}";

                // Actualizar DataGridView con estado de topics
                ActualizarDataGridViewSinoptico();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error actualizando visualización: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza el DataGridView con el estado actual de los topics del sinóptico
        /// </summary>
        private void ActualizarDataGridViewSinoptico()
        {
            try
            {
                if (dgvSinopticoEstado == null) return;

                // Crear DataTable para el estado del sinóptico
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("Tipo", typeof(string));
                dataTable.Columns.Add("Topic", typeof(string));
                dataTable.Columns.Add("Valor", typeof(string));
                dataTable.Columns.Add("Timestamp", typeof(string));
                dataTable.Columns.Add("Estado", typeof(string));
                dataTable.Columns.Add("Sincronizado", typeof(string));

                // Agregar topics de control
                foreach (var par in topicControlFeedback)
                {
                    string topicControl = par.Key;
                    string topicFeedback = par.Value;

                    string? valorControl = mqtt.LeerTopic(topicControl);
                    string? valorFeedback = mqtt.LeerTopic(topicFeedback);

                    // Determinar estado de sincronización
                    string sincronizado = "❓ N/A";
                    if (valorControl != null && valorFeedback != null)
                    {
                        sincronizado = valorControl == valorFeedback ? "✅ SÍ" : "❌ NO";
                    }
                    else if (valorControl != null && valorFeedback == null)
                    {
                        sincronizado = "⏳ Pendiente";
                    }

                    // Agregar fila de control
                    dataTable.Rows.Add(
                        "🎛️ Control",
                        topicControl.Replace("DD/ENAGAS/ALMENDRALEJO/", ""),
                        valorControl ?? "Sin datos",
                        DateTime.Now.ToString("HH:mm:ss"),
                        valorControl != null ? "✅ Activo" : "⏳ Esperando",
                        sincronizado
                    );

                    // Agregar fila de feedback
                    dataTable.Rows.Add(
                        "📤 Feedback",
                        topicFeedback.Replace("DD/ENAGAS/ALMENDRALEJO/", ""),
                        valorFeedback ?? "Sin datos",
                        DateTime.Now.ToString("HH:mm:ss"),
                        valorFeedback != null ? "✅ Activo" : "⏳ Esperando",
                        sincronizado
                    );
                }

                // Actualizar DataGridView
                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        dgvSinopticoEstado.DataSource = null;
                        dgvSinopticoEstado.DataSource = dataTable;
                        dgvSinopticoEstado.Refresh();
                    }));
                }
                else
                {
                    dgvSinopticoEstado.DataSource = null;
                    dgvSinopticoEstado.DataSource = dataTable;
                    dgvSinopticoEstado.Refresh();
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error actualizando DataGridView sinóptico: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Procesa cambios en topics de control y publica feedback automáticamente
        /// </summary>
        private void ProcesarCambiosTopicsControl()
        {
            try
            {
                if (!sinopticoHabilitado || mqtt == null || !mqtt.EstaConectado)
                    return;

                // Verificar cambios en cada topic de control
                foreach (var par in topicControlFeedback)
                {
                    string topicControl = par.Key;
                    string topicFeedback = par.Value;

                    // Obtener valor actual del topic de control
                    string? valorActual = mqtt.LeerTopic(topicControl);

                    if (valorActual != null)
                    {
                        // Verificar si el valor ha cambiado
                        if (!ultimosValoresControl.ContainsKey(topicControl) ||
                            ultimosValoresControl[topicControl] != valorActual)
                        {
                            // Valor ha cambiado, registrar y publicar feedback
                            string valorAnterior = ultimosValoresControl.ContainsKey(topicControl) ?
                                                 ultimosValoresControl[topicControl] : "N/A";

                            ultimosValoresControl[topicControl] = valorActual;

                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 SINÓPTICO: Cambio detectado en '{topicControl}'\n");
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 SINÓPTICO: Valor anterior: '{valorAnterior}' → Nuevo: '{valorActual}'\n");

                            // Publicar feedback automáticamente
                            PublicarFeedback(topicControl, topicFeedback, valorActual);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error procesando cambios: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Publica un valor en el topic de feedback correspondiente
        /// </summary>
        private async void PublicarFeedback(string topicControl, string topicFeedback, string valor)
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📤 SINÓPTICO: Publicando feedback...\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📤 SINÓPTICO: Control: '{topicControl}' → Feedback: '{topicFeedback}'\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📤 SINÓPTICO: Valor: '{valor}'\n");

                // Publicar en el topic de feedback
                bool publicado = await mqtt.EscribirTopic(topicFeedback, valor);

                if (publicado)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Feedback publicado exitosamente\n");

                    // Programar verificación de confirmación
                    Task.Delay(1000).ContinueWith(_ => VerificarConfirmacionFeedback(topicFeedback, valor));
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error publicando feedback\n");
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error en publicación: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Verifica que el feedback fue recibido correctamente
        /// </summary>
        private void VerificarConfirmacionFeedback(string topicFeedback, string valorEsperado)
        {
            try
            {
                if (!sinopticoHabilitado || mqtt == null || !mqtt.EstaConectado)
                    return;

                string? valorRecibido = mqtt.LeerTopic(topicFeedback);

                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        if (valorRecibido == valorEsperado)
                        {
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Confirmación exitosa en '{topicFeedback}'\n");
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Valor confirmado: '{valorRecibido}'\n");
                        }
                        else
                        {
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: Confirmación pendiente en '{topicFeedback}'\n");
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: Esperado: '{valorEsperado}', Recibido: '{valorRecibido ?? "null"}'\n");
                        }
                        rtbLog.ScrollToCaret();
                    }));
                }
            }
            catch (Exception ex)
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error verificando confirmación: {ex.Message}\n");
                        rtbLog.ScrollToCaret();
                    }));
                }
            }
        }

        private void panelLog_Paint(object sender, PaintEventArgs e)
        {

        }

        #endregion

        // FIN GESTIÓN DE LA NAVEGACIÓN ************************************************************************
    }
}
