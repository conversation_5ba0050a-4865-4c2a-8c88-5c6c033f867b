﻿using ControlDeProducciónENAGAS.Clases;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ControlDeProducciónENAGAS.Formularios
{
    public partial class MAIN : Form
    {


        public MAIN()
        {
            InitializeComponent();

            // Configura el estado inicial del formulario
            ConfigurarEstadoInicial();

            // Cargar configuración MQTT automáticamente
            CargarConfiguracionMQTT();

            // Inicializar DataGridView de topics MQTT
            InicializarDataGridViewTopics();

            // Inicializar botón de recarga de topics
            InicializarBotonRecargarTopics();
        }

        private ConexionMQTT? mqtt;
        private bool MQTTConectado = false;

        // Declaración del DataGridView para topics MQTT (agregar desde diseñador)
        private DataGridView? dgvMqttTopics;

        // Declaración del botón para recargar topics (agregar desde diseñador)
        private Button? btnRecargarTopics;


        // GESTIÓN DE LA NAVEGACIÓN Y DISEÑO ***************************************************************

        /// <summary>
        /// Método central para cambiar de pestaña y actualizar el estilo de los botones.
        /// </summary>
        /// <param name="paginaDestino">La TabPage que se va a mostrar.</param>
        /// <param name="botonActivo">El botón de navegación que se va a resaltar.</param>
        /// 
        private void ConfigurarEstadoInicial()
        {
            // Opcional: Oculta las cabeceras de las pestañas para que el usuario solo navegue con los botones.
            // Si quieres ver las pestañas, comenta o elimina las siguientes 3 líneas.
            tabControlPrincipal.Appearance = TabAppearance.FlatButtons;
            tabControlPrincipal.ItemSize = new Size(0, 1);
            tabControlPrincipal.SizeMode = TabSizeMode.Fixed;

            // Establece la pestaña de Correo como la inicial
            NavegarHacia(tabPageCorreo, btnNavCorreo);
        }
        private void NavegarHacia(TabPage paginaDestino, Button botonActivo)
        {
            // Cambia la pestaña activa en el TabControl
            if (tabControlPrincipal.SelectedTab != paginaDestino)
            {
                tabControlPrincipal.SelectedTab = paginaDestino;
            }

            // Actualiza el estilo de los botones
            ResetearEstilosBotones();
            ResaltarBoton(botonActivo);
        }

        /// <summary>
        /// Pone todos los botones de navegación en su estado "inactivo".
        /// </summary>
        private void ResetearEstilosBotones()
        {
            Color colorInactivo = Color.FromArgb(60, 63, 65);
            Font fuenteNormal = new Font("Segoe UI", 12F, FontStyle.Regular);

            btnNavCorreo.BackColor = colorInactivo;
            btnNavCorreo.Font = fuenteNormal;
            btnNavMqtt.BackColor = colorInactivo;
            btnNavMqtt.Font = fuenteNormal;
            btnNavModbus.BackColor = colorInactivo;
            btnNavModbus.Font = fuenteNormal;
            btnNavSinoptico.BackColor = colorInactivo;
            btnNavSinoptico.Font = fuenteNormal;
        }

        /// <summary>
        /// Resalta un botón de navegación para mostrar que está "activo".
        /// </summary>
        private void ResaltarBoton(Button boton)
        {
            boton.BackColor = Color.FromArgb(0, 122, 204);
            boton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
        }


        // --- EVENTOS CLICK DE LOS BOTONES DE NAVEGACIÓN ---

        private void btnNavCorreo_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageCorreo, btnNavCorreo);
        }

        private void btnNavMqtt_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageMqtt, btnNavMqtt);
        }

        private void btnNavModbus_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageModbus, btnNavModbus);
        }

        private void btnNavSinoptico_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageSinoptico, btnNavSinoptico);
        }
        private void btnCerrar_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        private void btnLimpiarLog_Click(object sender, EventArgs e)
        {
            rtbLog.Clear();
            rtbLog.AppendText("=== LOG DEL SISTEMA (limpiado) ===\n");
        }




        private async void btnMqttConectar_Click(object sender, EventArgs e)
        {
            try
            {
                // Validar puerto
                if (!int.TryParse(txtMqttPuerto.Text.Trim(), out int port))
                {
                    MessageBox.Show("El puerto MQTT debe ser un número válido.", "Puerto Inválido", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Puerto MQTT inválido\n");
                    return;
                }

                // Crear conexión con datos del formulario
                mqtt = new ConexionMQTT();

                // USAR LA IMPLEMENTACIÓN EXACTA DEL EJEMPLO FUNCIONAL
                bool conectado = await mqtt.ConectarAsync(
                    cmbMqttProtocolo.SelectedItem?.ToString() ?? "mqtts://",  // protocol
                    txtMqttHost.Text.Trim(),                                  // hostAddress
                    port,                                                     // port
                    txtMqttClientId.Text.Trim(),                             // clientId
                    txtMqttUsuario.Text.Trim(),                              // username
                    txtMqttPassword.Text,                                     // password (SIN Trim!)
                    chkMqttSslTls.Checked,                                   // useTlsFromCheckbox
                    true,                                                     // isCASigned (CA signed certificate)
                    false                                                     // useAlpn
                );

                if (conectado)
                {
                    lblEstadoMqtt.Text = "MQTT: Conectado ✅";
                    panelEstadoMqtt.BackColor = Color.Green;
                    btnMqttConectar.Enabled = false;
                    btnMqttDesconectar.Enabled = true;

                    // Habilitar botón de recarga
                    if (btnRecargarTopics != null)
                        btnRecargarTopics.Enabled = true;

                    // Suscribir topics del CSV
                    SuscribirTopicsDelCSV();

                    // Habilitar Lectura de Topics
                    MQTTConectado = true;

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Conectado a MQTT: {txtMqttHost.Text}:{port}\n");

                    // DIAGNÓSTICO COMPLETO
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 INICIANDO DIAGNÓSTICO COMPLETO...\n");

                    // Test 1: Verificar DataGridView
                    InicializarDataGridViewTopics();

                    // Test 2: Probar con datos de ejemplo
                    TestDataGridViewWithSampleData();

                    // Test 3: Verificar almacén de topics MQTT
                    TestMqttTopicsStorage();

                    // Test 4: Forzar actualización
                    ActualizarListaTopics(verbose: true);

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 DIAGNÓSTICO COMPLETADO\n");
                    rtbLog.ScrollToCaret();
                }
                else
                {
                    MessageBox.Show("No se pudo conectar al broker MQTT");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error conectando a MQTT\n");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error al conectar MQTT: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error conectando a MQTT: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }


        private async void SuscribirTopicsDelCSV()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 Iniciando suscripción de topics desde CSV...\n");

                if (!File.Exists("topics.csv"))
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ topics.csv no existe, creando archivo de ejemplo...\n");
                    File.WriteAllText("topics.csv", "enagas/temperatura\nenagas/presion\nenagas/estado\nenagas/caudal");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Archivo topics.csv creado\n");
                }

                string[] topics = File.ReadAllLines("topics.csv");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 Leyendo {topics.Length} líneas de topics.csv\n");

                int topicsSuscritos = 0;
                foreach (string topic in topics)
                {
                    if (!topic.StartsWith("#") && !string.IsNullOrEmpty(topic.Trim()))
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📡 Suscribiendo a: {topic.Trim()}\n");
                        await mqtt.SuscribirTopic(topic.Trim());
                        topicsSuscritos++;
                    }
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Suscripción completada: {topicsSuscritos} topics\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error en suscripción de topics: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            // Solo actualizar si MQTT está conectado (sin logs verbosos)
            if (mqtt == null || !mqtt.EstaConectado) return;

            try
            {
                // Actualizar lista de topics en DataGridView silenciosamente
                ActualizarListaTopics();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TIMER ERROR: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        private void btnMqttDesconectar_Click(object sender, EventArgs e)
        {
            mqtt?.Desconectar();

            lblEstadoMqtt.Text = "MQTT: Desconectado ❌";
            panelEstadoMqtt.BackColor = Color.Gray;
            btnMqttConectar.Enabled = true;
            btnMqttDesconectar.Enabled = false;
            MQTTConectado = false;

            // Deshabilitar botón de recarga
            if (btnRecargarTopics != null)
                btnRecargarTopics.Enabled = false;

            // Limpiar lista de topics
            if (dgvMqttTopics != null)
            {
                dgvMqttTopics.DataSource = null;
            }
        }

        /// <summary>
        /// Carga la configuración MQTT desde el archivo JSON y la aplica al formulario
        /// </summary>
        private void CargarConfiguracionMQTT()
        {
            try
            {
                var config = ConfiguracionMQTT.CargarDesdeArchivo();

                // Aplicar configuración a los controles del formulario
                cmbMqttProtocolo.SelectedItem = config.Protocolo;
                txtMqttHost.Text = config.Host;
                txtMqttPuerto.Text = config.Puerto.ToString();
                txtMqttClientId.Text = config.ClientId;
                txtMqttUsuario.Text = config.Usuario;
                txtMqttPassword.Text = config.Password;
                chkMqttSslTls.Checked = config.UsarSslTls;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚙️ Configuración MQTT cargada: {config.Name}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🌐 Servidor: {config.Host}:{config.Puerto}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔒 SSL/TLS: {(config.UsarSslTls ? "Activado" : "Desactivado")}\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error cargando configuración: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }




        /// <summary>
        /// Inicializa el DataGridView para mostrar los topics MQTT (solo verifica configuración del Designer)
        /// </summary>
        private void InicializarDataGridViewTopics()
        {
            try
            {
                // Verificar que el control existe (debe ser agregado desde el diseñador)
                if (dgvMqttTopics == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CRITICAL: DataGridView dgvMqttTopics not found - must be added in Designer\n");
                    return;
                }

                // Verificar configuración del Designer
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 DataGridView found - Columns: {dgvMqttTopics.Columns.Count}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 AutoGenerateColumns: {dgvMqttTopics.AutoGenerateColumns}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 AllowUserToAddRows: {dgvMqttTopics.AllowUserToAddRows}\n");

                // Listar columnas configuradas en el Designer
                for (int i = 0; i < dgvMqttTopics.Columns.Count; i++)
                {
                    var col = dgvMqttTopics.Columns[i];
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 Column {i}: Name='{col.Name}', DataPropertyName='{col.DataPropertyName}', HeaderText='{col.HeaderText}'\n");
                }

                if (dgvMqttTopics.Columns.Count == 0)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ WARNING: No columns configured in Designer - please add columns manually\n");
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ DataGridView properly configured in Designer\n");
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error checking DataGridView configuration: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Inicializa el botón de recarga de topics
        /// </summary>
        private void InicializarBotonRecargarTopics()
        {
            try
            {
                if (btnRecargarTopics == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ Button btnRecargarTopics not found - must be added in Designer\n");
                    return;
                }

                // Configurar el botón
                btnRecargarTopics.Enabled = false; // Inicialmente deshabilitado
                btnRecargarTopics.Click += BtnRecargarTopics_Click;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Reload button configured\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error configuring reload button: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Evento del botón de recarga de topics
        /// </summary>
        private void BtnRecargarTopics_Click(object? sender, EventArgs e)
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 Manual reload requested\n");
                ActualizarListaTopics(verbose: true);
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Manual reload completed\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error in manual reload: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza la lista de topics en el DataGridView (solo data binding)
        /// </summary>
        private void ActualizarListaTopics(bool verbose = false)
        {
            try
            {
                // Step 1: Verify DataGridView exists
                if (dgvMqttTopics == null)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ DEBUG: dgvMqttTopics is null - check Designer\n");
                    return;
                }

                // Step 2: Verify MQTT connection
                if (mqtt == null)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ DEBUG: mqtt object is null\n");
                    return;
                }

                if (!mqtt.EstaConectado)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ DEBUG: MQTT not connected\n");
                    return;
                }

                // Step 3: Get ALL subscribed topics (including those without values)
                var listaTopics = new List<object>();

                // First, get topics from MQTT storage
                var topicsData = mqtt.ObtenerTodosLosTopics();
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: MQTT storage has {topicsData.Count} topics with values\n");

                // Add topics with values
                foreach (var topic in topicsData)
                {
                    listaTopics.Add(new
                    {
                        Topic = topic.Key,
                        Valor = topic.Value ?? "Sin datos",
                        Timestamp = DateTime.Now.ToString("HH:mm:ss"),
                        Estado = string.IsNullOrEmpty(topic.Value) ? "⏳ Esperando" : "✅ Activo"
                    });
                }

                // Step 4: Also add subscribed topics from CSV that might not have values yet
                if (File.Exists("topics.csv"))
                {
                    string[] csvTopics = File.ReadAllLines("topics.csv");
                    foreach (string csvTopic in csvTopics)
                    {
                        if (!csvTopic.StartsWith("#") && !string.IsNullOrEmpty(csvTopic.Trim()))
                        {
                            string topicName = csvTopic.Trim();
                            // Only add if not already in the list
                            if (!topicsData.ContainsKey(topicName))
                            {
                                listaTopics.Add(new
                                {
                                    Topic = topicName,
                                    Valor = "Sin datos",
                                    Timestamp = DateTime.Now.ToString("HH:mm:ss"),
                                    Estado = "⏳ Esperando"
                                });
                            }
                        }
                    }
                }

                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: Total topics to display: {listaTopics.Count}\n");

                // Step 5: Update DataGridView (data binding only)
                if (InvokeRequired)
                {
                    Invoke(new Action(() => BindDataToGrid(listaTopics, verbose)));
                }
                else
                {
                    BindDataToGrid(listaTopics, verbose);
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ ERROR in ActualizarListaTopics: {ex.Message}\n");
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Stack trace: {ex.StackTrace}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Binds data to DataGridView (separated for debugging)
        /// </summary>
        private void BindDataToGrid(List<object> data, bool verbose = false)
        {
            try
            {
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 DEBUG: Starting data binding...\n");

                dgvMqttTopics.DataSource = null;
                dgvMqttTopics.DataSource = data;

                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ DEBUG: Data binding completed\n");
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: DataGridView now has {dgvMqttTopics.Rows.Count} rows\n");

                dgvMqttTopics.Refresh();
                if (verbose) rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ ERROR in BindDataToGrid: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        private void btnMqttDesconectar_Click_1(object sender, EventArgs e)
        {

        }

        /// <summary>
        /// TEST METHOD: Load sample topics to verify DataGridView configuration
        /// Call this method to test if DataGridView can display data
        /// </summary>
        public void TestDataGridViewWithSampleData()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Starting DataGridView test with sample data\n");

                if (dgvMqttTopics == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: dgvMqttTopics is null\n");
                    return;
                }

                // Create sample data matching the expected structure
                var sampleData = new List<object>
                {
                    new { Topic = "test/topic1", Valor = "Sample Value 1", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "✅ Active" },
                    new { Topic = "test/topic2", Valor = "Sample Value 2", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "✅ Active" },
                    new { Topic = "test/topic3", Valor = "No data", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "⏳ Waiting" }
                };

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Binding {sampleData.Count} sample records\n");

                dgvMqttTopics.DataSource = null;
                dgvMqttTopics.DataSource = sampleData;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: DataGridView now shows {dgvMqttTopics.Rows.Count} rows\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ TEST COMPLETED: Check if data appears in DataGridView\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// TEST METHOD: Check MQTT topics storage
        /// Call this method to verify if MQTT topics are being received and stored
        /// </summary>
        public void TestMqttTopicsStorage()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Checking MQTT topics storage\n");

                if (mqtt == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST: MQTT object is null\n");
                    return;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: MQTT Connected: {mqtt.EstaConectado}\n");

                var topics = mqtt.ObtenerTodosLosTopics();
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Total topics in storage: {topics.Count}\n");

                if (topics.Count == 0)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ TEST: No topics found - check MQTT subscription\n");
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 TEST: Topics in storage:\n");
                    foreach (var topic in topics)
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋   - '{topic.Key}' = '{topic.Value}'\n");
                    }
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        // FIN GESTIÓN DE LA NAVEGACIÓN ************************************************************************
    }
}
