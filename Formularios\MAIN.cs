﻿using ControlDeProducciónENAGAS.Clases;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ControlDeProducciónENAGAS.Formularios
{
    public partial class MAIN : Form
    {


        public MAIN()
        {
            InitializeComponent();

            // Configura el estado inicial del formulario
            ConfigurarEstadoInicial();

            // Cargar configuración MQTT automáticamente
            CargarConfiguracionMQTT();

            // Inicializar DataGridView de topics MQTT
            InicializarDataGridViewTopics();

            // Inicializar botón de refrescar MQTT
            InicializarBotonRefrescarMQTT();

            // Inicializar sistema sinóptico
            InicializarSistemaSinoptico();

            // Inicializar controles de visualización sinóptico
            InicializarVisualizacionSinoptico();

            // Cargar configuración Modbus automáticamente
            CargarConfiguracionModbus();
        }

        private ConexionMQTT? mqtt;
        private bool MQTTConectado = false;

        // Declaración del DataGridView para topics MQTT (agregar desde diseñador)
        private DataGridView? dgvMqttTopics;

        // El botón btnRefrescarMQTT ya está declarado en el Designer

        // Sistema de monitorización bidireccional para sinóptico
        private readonly Dictionary<string, string> topicControlFeedback = new Dictionary<string, string>
        {
            { "DD/ENAGAS/ALMENDRALEJO/CONSIGNAPA", "DD/ENAGAS/ALMENDRALEJO/FEEDBACKPA" },
            { "DD/ENAGAS/ALMENDRALEJO/MOTIVOPA", "DD/ENAGAS/ALMENDRALEJO/FEEDBACKMOTIVOPA" }
        };

        private readonly Dictionary<string, string> ultimosValoresControl = new Dictionary<string, string>();
        private bool sinopticoHabilitado = false;

        // Los controles del sinóptico ahora están en el Designer
        // No necesitamos declaraciones privadas adicionales

        // Variables para Modbus
        private ConexionModbus? modbus;
        private ConfiguracionModbus? configModbus;


        // GESTIÓN DE LA NAVEGACIÓN Y DISEÑO ***************************************************************

        /// <summary>
        /// Método central para cambiar de pestaña y actualizar el estilo de los botones.
        /// </summary>
        /// <param name="paginaDestino">La TabPage que se va a mostrar.</param>
        /// <param name="botonActivo">El botón de navegación que se va a resaltar.</param>
        /// 
        private void ConfigurarEstadoInicial()
        {
            // Opcional: Oculta las cabeceras de las pestañas para que el usuario solo navegue con los botones.
            // Si quieres ver las pestañas, comenta o elimina las siguientes 3 líneas.
            tabControlPrincipal.Appearance = TabAppearance.FlatButtons;
            tabControlPrincipal.ItemSize = new Size(0, 1);
            tabControlPrincipal.SizeMode = TabSizeMode.Fixed;

            // Establece la pestaña de Correo como la inicial
            NavegarHacia(tabPageCorreo, btnNavCorreo);
        }
        private void NavegarHacia(TabPage paginaDestino, Button botonActivo)
        {
            // Cambia la pestaña activa en el TabControl
            if (tabControlPrincipal.SelectedTab != paginaDestino)
            {
                tabControlPrincipal.SelectedTab = paginaDestino;
            }

            // Actualiza el estilo de los botones
            ResetearEstilosBotones();
            ResaltarBoton(botonActivo);
        }

        /// <summary>
        /// Pone todos los botones de navegación en su estado "inactivo".
        /// </summary>
        private void ResetearEstilosBotones()
        {
            Color colorInactivo = Color.FromArgb(60, 63, 65);
            Font fuenteNormal = new Font("Segoe UI", 12F, FontStyle.Regular);

            btnNavCorreo.BackColor = colorInactivo;
            btnNavCorreo.Font = fuenteNormal;
            btnNavMqtt.BackColor = colorInactivo;
            btnNavMqtt.Font = fuenteNormal;
            btnNavModbus.BackColor = colorInactivo;
            btnNavModbus.Font = fuenteNormal;
            btnNavSinoptico.BackColor = colorInactivo;
            btnNavSinoptico.Font = fuenteNormal;
        }

        /// <summary>
        /// Resalta un botón de navegación para mostrar que está "activo".
        /// </summary>
        private void ResaltarBoton(Button boton)
        {
            boton.BackColor = Color.FromArgb(0, 122, 204);
            boton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
        }


        // --- EVENTOS CLICK DE LOS BOTONES DE NAVEGACIÓN ---

        private void btnNavCorreo_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageCorreo, btnNavCorreo);
        }

        private void btnNavMqtt_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageMqtt, btnNavMqtt);
        }

        private void btnNavModbus_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageModbus, btnNavModbus);
        }

        private void btnNavSinoptico_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageSinoptico, btnNavSinoptico);
        }
        private void btnCerrar_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        private void btnLimpiarLog_Click(object sender, EventArgs e)
        {
            rtbLog.Clear();
            rtbLog.AppendText("=== LOG DEL SISTEMA (limpiado) ===\n");
        }




        private async void btnMqttConectar_Click(object sender, EventArgs e)
        {
            try
            {
                // Validar puerto
                if (!int.TryParse(txtMqttPuerto.Text.Trim(), out int port))
                {
                    MessageBox.Show("El puerto MQTT debe ser un número válido.", "Puerto Inválido", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Puerto MQTT inválido\n");
                    return;
                }

                // Crear conexión con datos del formulario
                mqtt = new ConexionMQTT();

                // USAR LA IMPLEMENTACIÓN EXACTA DEL EJEMPLO FUNCIONAL
                bool conectado = await mqtt.ConectarAsync(
                    cmbMqttProtocolo.SelectedItem?.ToString() ?? "mqtts://",  // protocol
                    txtMqttHost.Text.Trim(),                                  // hostAddress
                    port,                                                     // port
                    txtMqttClientId.Text.Trim(),                             // clientId
                    txtMqttUsuario.Text.Trim(),                              // username
                    txtMqttPassword.Text,                                     // password (SIN Trim!)
                    chkMqttSslTls.Checked,                                   // useTlsFromCheckbox
                    true,                                                     // isCASigned (CA signed certificate)
                    false                                                     // useAlpn
                );

                if (conectado)
                {
                    lblEstadoMqtt.Text = "MQTT: Conectado ✅";
                    panelEstadoMqtt.BackColor = Color.Green;
                    btnMqttConectar.Enabled = false;
                    btnMqttDesconectar.Enabled = true;

                    // Habilitar botón de refrescar MQTT
                    if (btnRefrescarMQTT != null)
                        btnRefrescarMQTT.Enabled = true;

                    // Suscribir topics del CSV
                    SuscribirTopicsDelCSV();

                    // Suscribir topics del sinóptico
                    SuscribirTopicsSinoptico();

                    // Habilitar Lectura de Topics
                    MQTTConectado = true;

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Conectado a MQTT: {txtMqttHost.Text}:{port}\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 💡 Usa el botón 'Refrescar MQTT' para ver los topics disponibles\n");
                    rtbLog.ScrollToCaret();
                }
                else
                {
                    MessageBox.Show("No se pudo conectar al broker MQTT");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error conectando a MQTT\n");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error al conectar MQTT: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error conectando a MQTT: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }


        private async void SuscribirTopicsDelCSV()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 Iniciando suscripción de topics desde CSV...\n");

                if (!File.Exists("topics.csv"))
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ topics.csv no existe, creando archivo de ejemplo...\n");
                    File.WriteAllText("topics.csv", "enagas/temperatura\nenagas/presion\nenagas/estado\nenagas/caudal");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Archivo topics.csv creado\n");
                }

                string[] topics = File.ReadAllLines("topics.csv");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 Leyendo {topics.Length} líneas de topics.csv\n");

                int topicsSuscritos = 0;
                foreach (string topic in topics)
                {
                    if (!topic.StartsWith("#") && !string.IsNullOrEmpty(topic.Trim()))
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📡 Suscribiendo a: {topic.Trim()}\n");
                        await mqtt.SuscribirTopic(topic.Trim());
                        topicsSuscritos++;
                    }
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Suscripción completada: {topicsSuscritos} topics\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error en suscripción de topics: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            // Timer ya no actualiza automáticamente el DataGridView
            // Solo se actualiza manualmente con el botón btnRefrescarMQTT

            // Procesar cambios en topics de control del sinóptico
            ProcesarCambiosTopicsControl();

            // Actualizar visualización del sinóptico
            ActualizarVisualizacionSinoptico();
        }

        private void btnMqttDesconectar_Click(object sender, EventArgs e)
        {
            mqtt?.Desconectar();

            lblEstadoMqtt.Text = "MQTT: Desconectado ❌";
            panelEstadoMqtt.BackColor = Color.Gray;
            btnMqttConectar.Enabled = true;
            btnMqttDesconectar.Enabled = false;
            MQTTConectado = false;

            // Deshabilitar botón de refrescar MQTT
            if (btnRefrescarMQTT != null)
                btnRefrescarMQTT.Enabled = false;

            // Deshabilitar sistema sinóptico
            DeshabilitarSistemaSinoptico();

            // Limpiar lista de topics
            if (dgvMqttTopics != null)
            {
                dgvMqttTopics.DataSource = null;
            }
        }

        /// <summary>
        /// Carga la configuración MQTT desde el archivo JSON y la aplica al formulario
        /// </summary>
        private void CargarConfiguracionMQTT()
        {
            try
            {
                var config = ConfiguracionMQTT.CargarDesdeArchivo();

                // Aplicar configuración a los controles del formulario
                cmbMqttProtocolo.SelectedItem = config.Protocolo;
                txtMqttHost.Text = config.Host;
                txtMqttPuerto.Text = config.Puerto.ToString();
                txtMqttClientId.Text = config.ClientId;
                txtMqttUsuario.Text = config.Usuario;
                txtMqttPassword.Text = config.Password;
                chkMqttSslTls.Checked = config.UsarSslTls;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚙️ Configuración MQTT cargada: {config.Name}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🌐 Servidor: {config.Host}:{config.Puerto}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔒 SSL/TLS: {(config.UsarSslTls ? "Activado" : "Desactivado")}\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error cargando configuración: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }




        /// <summary>
        /// Inicializa el DataGridView para mostrar los topics MQTT (SOLO verifica, NO modifica Designer)
        /// </summary>
        private void InicializarDataGridViewTopics()
        {
            try
            {
                // Verificar que el control existe (debe ser agregado desde el diseñador)
                if (dgvMqttTopics == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CRITICAL: DataGridView dgvMqttTopics not found - must be added in Designer\n");
                    return;
                }

                // SOLO VERIFICAR - NO MODIFICAR NADA DEL DESIGNER
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 DataGridView found - Columns: {dgvMqttTopics.Columns.Count}\n");

                // Listar columnas configuradas en el Designer
                for (int i = 0; i < dgvMqttTopics.Columns.Count; i++)
                {
                    var col = dgvMqttTopics.Columns[i];
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 Column {i}: Name='{col.Name}', DataPropertyName='{col.DataPropertyName}'\n");
                }

                if (dgvMqttTopics.Columns.Count == 0)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ WARNING: No columns configured in Designer\n");
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ DataGridView configured in Designer with {dgvMqttTopics.Columns.Count} columns\n");
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error checking DataGridView: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Inicializa el botón de refrescar MQTT topics
        /// </summary>
        private void InicializarBotonRefrescarMQTT()
        {
            try
            {
                if (btnRefrescarMQTT == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ Button btnRefrescarMQTT not found - must be added in Designer\n");
                    return;
                }

                // Configurar el botón (el evento Click ya está configurado en el Designer)
                btnRefrescarMQTT.Enabled = false; // Inicialmente deshabilitado

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Refresh MQTT button configured\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error configuring refresh button: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Evento del botón de refrescar MQTT topics
        /// </summary>
        private void btnRefrescarMQTT_Click(object? sender, EventArgs e)
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 Manual MQTT refresh requested\n");
                ActualizarListaTopics(verbose: true);
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Manual MQTT refresh completed\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error in manual MQTT refresh: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza la lista de topics en el DataGridView usando DataTable para compatibilidad con Designer
        /// </summary>
        private void ActualizarListaTopics(bool verbose = false)
        {
            try
            {
                // Step 1: Verify DataGridView exists
                if (dgvMqttTopics == null)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ DEBUG: dgvMqttTopics is null - check Designer\n");
                    return;
                }

                // Step 2: Verify MQTT connection
                if (mqtt == null)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ DEBUG: mqtt object is null\n");
                    return;
                }

                if (!mqtt.EstaConectado)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ DEBUG: MQTT not connected\n");
                    return;
                }

                // Step 3: Create DataTable compatible with Designer columns
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("Topic", typeof(string));
                dataTable.Columns.Add("Valor", typeof(string));
                dataTable.Columns.Add("Timestamp", typeof(string));
                dataTable.Columns.Add("Estado", typeof(string));

                // Step 4: Get topics from MQTT storage
                var topicsData = mqtt.ObtenerTodosLosTopics();
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: MQTT storage has {topicsData.Count} topics with values\n");

                // Add topics with values
                foreach (var topic in topicsData)
                {
                    dataTable.Rows.Add(
                        topic.Key,
                        topic.Value ?? "Sin datos",
                        DateTime.Now.ToString("HH:mm:ss"),
                        string.IsNullOrEmpty(topic.Value) ? "⏳ Esperando" : "✅ Activo"
                    );
                }

                // Step 5: Also add subscribed topics from CSV that might not have values yet
                if (File.Exists("topics.csv"))
                {
                    string[] csvTopics = File.ReadAllLines("topics.csv");
                    foreach (string csvTopic in csvTopics)
                    {
                        if (!csvTopic.StartsWith("#") && !string.IsNullOrEmpty(csvTopic.Trim()))
                        {
                            string topicName = csvTopic.Trim();
                            // Only add if not already in the list
                            if (!topicsData.ContainsKey(topicName))
                            {
                                dataTable.Rows.Add(
                                    topicName,
                                    "Sin datos",
                                    DateTime.Now.ToString("HH:mm:ss"),
                                    "⏳ Esperando"
                                );
                            }
                        }
                    }
                }

                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: Total topics to display: {dataTable.Rows.Count}\n");

                // Step 6: Update DataGridView using DataTable
                if (InvokeRequired)
                {
                    Invoke(new Action(() => BindDataTableToGrid(dataTable, verbose)));
                }
                else
                {
                    BindDataTableToGrid(dataTable, verbose);
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ ERROR in ActualizarListaTopics: {ex.Message}\n");
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Stack trace: {ex.StackTrace}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Binds DataTable to DataGridView (compatible with Designer columns)
        /// </summary>
        private void BindDataTableToGrid(DataTable dataTable, bool verbose = false)
        {
            try
            {
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 DEBUG: Starting DataTable binding...\n");

                // Clear current data source
                dgvMqttTopics.DataSource = null;

                // Bind DataTable
                dgvMqttTopics.DataSource = dataTable;

                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ DEBUG: DataTable binding completed\n");
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: DataGridView now has {dgvMqttTopics.Rows.Count} rows\n");

                // Force refresh
                dgvMqttTopics.Refresh();
                dgvMqttTopics.Invalidate();

                if (verbose) rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ ERROR in BindDataTableToGrid: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        private void btnMqttDesconectar_Click_1(object sender, EventArgs e)
        {

        }

        /// <summary>
        /// TEST METHOD: Load sample topics to verify DataGridView configuration
        /// Call this method to test if DataGridView can display data
        /// </summary>
        public void TestDataGridViewWithSampleData()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Starting DataGridView test with sample data\n");

                if (dgvMqttTopics == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: dgvMqttTopics is null\n");
                    return;
                }

                // Create sample data matching the expected structure
                var sampleData = new List<object>
                {
                    new { Topic = "test/topic1", Valor = "Sample Value 1", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "✅ Active" },
                    new { Topic = "test/topic2", Valor = "Sample Value 2", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "✅ Active" },
                    new { Topic = "test/topic3", Valor = "No data", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "⏳ Waiting" }
                };

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Binding {sampleData.Count} sample records\n");

                dgvMqttTopics.DataSource = null;
                dgvMqttTopics.DataSource = sampleData;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: DataGridView now shows {dgvMqttTopics.Rows.Count} rows\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ TEST COMPLETED: Check if data appears in DataGridView\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// TEST METHOD: Check MQTT topics storage
        /// Call this method to verify if MQTT topics are being received and stored
        /// </summary>
        public void TestMqttTopicsStorage()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Checking MQTT topics storage\n");

                if (mqtt == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST: MQTT object is null\n");
                    return;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: MQTT Connected: {mqtt.EstaConectado}\n");

                var topics = mqtt.ObtenerTodosLosTopics();
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Total topics in storage: {topics.Count}\n");

                if (topics.Count == 0)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ TEST: No topics found - check MQTT subscription\n");
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 TEST: Topics in storage:\n");
                    foreach (var topic in topics)
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋   - '{topic.Key}' = '{topic.Value}'\n");
                    }
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        #region Sistema Sinóptico MQTT Bidireccional

        /// <summary>
        /// Inicializa el sistema de monitorización bidireccional del sinóptico
        /// </summary>
        private void InicializarSistemaSinoptico()
        {
            try
            {
                // Inicializar diccionario de últimos valores
                foreach (var topicControl in topicControlFeedback.Keys)
                {
                    ultimosValoresControl[topicControl] = string.Empty;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Sistema sinóptico inicializado\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error inicializando sistema sinóptico: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Suscribe a todos los topics del sinóptico (control y feedback)
        /// </summary>
        private async void SuscribirTopicsSinoptico()
        {
            try
            {
                if (mqtt == null || !mqtt.EstaConectado)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: MQTT no conectado\n");
                    return;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 SINÓPTICO: Suscribiendo a topics...\n");

                // Suscribir a topics de control
                foreach (var topicControl in topicControlFeedback.Keys)
                {
                    await mqtt.SuscribirTopic(topicControl);
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📡 SINÓPTICO: Suscrito a control '{topicControl}'\n");
                }

                // Suscribir a topics de feedback
                foreach (var topicFeedback in topicControlFeedback.Values)
                {
                    await mqtt.SuscribirTopic(topicFeedback);
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📡 SINÓPTICO: Suscrito a feedback '{topicFeedback}'\n");
                }

                sinopticoHabilitado = true;
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Sistema habilitado y suscripciones completadas\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error en suscripciones: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Deshabilita el sistema sinóptico
        /// </summary>
        private void DeshabilitarSistemaSinoptico()
        {
            try
            {
                sinopticoHabilitado = false;
                ultimosValoresControl.Clear();

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: Sistema deshabilitado\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error deshabilitando: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        #region Configuración y Métodos Modbus

        /// <summary>
        /// Carga la configuración Modbus automáticamente al iniciar
        /// </summary>
        private void CargarConfiguracionModbus()
        {
            try
            {
                configModbus = ConfiguracionModbus.CargarDesdeArchivo();

                // Aplicar configuración a los controles
                txtModbusIp.Text = configModbus.Ip;
                nudModbusPuerto.Value = configModbus.Puerto;
                nudModbusDeviceId.Value = 255; // Device ID por defecto Schneider Electric

                // Configurar estado inicial de botones
                btnModbusConectar.Enabled = true;
                //btnModbusLeerRegistros.Enabled = false;
                btnModbusEscribirRegistro.Enabled = false;
                btnModbusDesconectar.Enabled = false;
                lblModbusValorLeido.Text = "Valor: --";

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Configuración cargada - {configModbus.Name}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔧 MODBUS: {configModbus.Ip}:{configModbus.Puerto} (Device ID: {configModbus.DeviceId})\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error cargando configuración Modbus: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }



        #endregion

        /// <summary>
        /// Inicializa los controles de visualización del sinóptico usando controles del DESIGNER
        /// </summary>
        private void InicializarVisualizacionSinoptico()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Inicializando controles del Designer\n");

                // Los controles ya están creados en el Designer, solo configuramos el DataGridView
                // Configurar el DataGridView que ya existe en el Designer
                // Configurar estilo del DataGridView existente
                dgvSinopticoTopics.DefaultCellStyle.BackColor = Color.FromArgb(37, 37, 38);
                dgvSinopticoTopics.DefaultCellStyle.ForeColor = Color.White;
                dgvSinopticoTopics.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 122, 204);
                dgvSinopticoTopics.DefaultCellStyle.SelectionForeColor = Color.White;
                dgvSinopticoTopics.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(45, 45, 48);
                dgvSinopticoTopics.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
                dgvSinopticoTopics.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9, FontStyle.Bold);
                dgvSinopticoTopics.AutoGenerateColumns = false;

                // Crear columnas para el DataGridView
                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Tipo",
                    HeaderText = "Tipo",
                    DataPropertyName = "Tipo",
                    Width = 100
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Topic",
                    HeaderText = "Topic",
                    DataPropertyName = "Topic",
                    Width = 200
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Valor",
                    HeaderText = "Valor",
                    DataPropertyName = "Valor",
                    Width = 120
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Timestamp",
                    HeaderText = "Timestamp",
                    DataPropertyName = "Timestamp",
                    Width = 100
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Estado",
                    HeaderText = "Estado",
                    DataPropertyName = "Estado",
                    Width = 120
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Sincronizado",
                    HeaderText = "Sincronizado",
                    DataPropertyName = "Sincronizado",
                    Width = 120
                });

                // Los controles ya están agregados en el Designer
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Controles del Designer configurados correctamente\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 SINÓPTICO: DataGridView configurado con {dgvSinopticoTopics.Columns.Count} columnas\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error creando visualización sinóptico: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza la visualización del estado del sinóptico
        /// </summary>
        private void ActualizarVisualizacionSinoptico()
        {
            try
            {
                if (!sinopticoHabilitado || mqtt == null || !mqtt.EstaConectado)
                {
                    // Sistema deshabilitado
                    if (lblSinopticoEstado != null)
                        lblSinopticoEstado.Text = "🔴 Sistema Sinóptico: DESHABILITADO";

                    if (dgvSinopticoTopics != null)
                        dgvSinopticoTopics.DataSource = null;

                    return;
                }

                // Sistema habilitado - actualizar estado
                if (lblSinopticoEstado != null)
                    lblSinopticoEstado.Text = "🟢 Sistema Sinóptico: ACTIVO";

                if (lblSinopticoUltimaActualizacion != null)
                    lblSinopticoUltimaActualizacion.Text = $"Última Actualización: {DateTime.Now:HH:mm:ss}";

                // Actualizar DataGridView con estado de topics
                ActualizarDataGridViewSinoptico();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error actualizando visualización: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza el DataGridView con el estado actual de los topics del sinóptico
        /// </summary>
        private void ActualizarDataGridViewSinoptico()
        {
            try
            {
                if (dgvSinopticoTopics == null) return;

                // Crear DataTable para el estado del sinóptico
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("Tipo", typeof(string));
                dataTable.Columns.Add("Topic", typeof(string));
                dataTable.Columns.Add("Valor", typeof(string));
                dataTable.Columns.Add("Timestamp", typeof(string));
                dataTable.Columns.Add("Estado", typeof(string));
                dataTable.Columns.Add("Sincronizado", typeof(string));

                // Agregar topics de control
                foreach (var par in topicControlFeedback)
                {
                    string topicControl = par.Key;
                    string topicFeedback = par.Value;

                    string? valorControl = mqtt.LeerTopic(topicControl);
                    string? valorFeedback = mqtt.LeerTopic(topicFeedback);

                    // Determinar estado de sincronización
                    string sincronizado = "❓ N/A";
                    if (valorControl != null && valorFeedback != null)
                    {
                        sincronizado = valorControl == valorFeedback ? "✅ SÍ" : "❌ NO";
                    }
                    else if (valorControl != null && valorFeedback == null)
                    {
                        sincronizado = "⏳ Pendiente";
                    }

                    // Agregar fila de control
                    dataTable.Rows.Add(
                        "🎛️ Control",
                        topicControl.Replace("DD/ENAGAS/ALMENDRALEJO/", ""),
                        valorControl ?? "Sin datos",
                        DateTime.Now.ToString("HH:mm:ss"),
                        valorControl != null ? "✅ Activo" : "⏳ Esperando",
                        sincronizado
                    );

                    // Agregar fila de feedback
                    dataTable.Rows.Add(
                        "📤 Feedback",
                        topicFeedback.Replace("DD/ENAGAS/ALMENDRALEJO/", ""),
                        valorFeedback ?? "Sin datos",
                        DateTime.Now.ToString("HH:mm:ss"),
                        valorFeedback != null ? "✅ Activo" : "⏳ Esperando",
                        sincronizado
                    );
                }

                // Actualizar DataGridView
                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        dgvSinopticoTopics.DataSource = null;
                        dgvSinopticoTopics.DataSource = dataTable;
                        dgvSinopticoTopics.Refresh();
                    }));
                }
                else
                {
                    dgvSinopticoTopics.DataSource = null;
                    dgvSinopticoTopics.DataSource = dataTable;
                    dgvSinopticoTopics.Refresh();
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error actualizando DataGridView sinóptico: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Procesa cambios en topics de control y publica feedback automáticamente
        /// </summary>
        private void ProcesarCambiosTopicsControl()
        {
            try
            {
                if (!sinopticoHabilitado || mqtt == null || !mqtt.EstaConectado)
                    return;

                // Verificar cambios en cada topic de control
                foreach (var par in topicControlFeedback)
                {
                    string topicControl = par.Key;
                    string topicFeedback = par.Value;

                    // Obtener valor actual del topic de control
                    string? valorActual = mqtt.LeerTopic(topicControl);

                    if (valorActual != null)
                    {
                        // Verificar si el valor ha cambiado
                        if (!ultimosValoresControl.ContainsKey(topicControl) ||
                            ultimosValoresControl[topicControl] != valorActual)
                        {
                            // Valor ha cambiado, registrar y publicar feedback
                            string valorAnterior = ultimosValoresControl.ContainsKey(topicControl) ?
                                                 ultimosValoresControl[topicControl] : "N/A";

                            ultimosValoresControl[topicControl] = valorActual;

                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 SINÓPTICO: Cambio detectado en '{topicControl}'\n");
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 SINÓPTICO: Valor anterior: '{valorAnterior}' → Nuevo: '{valorActual}'\n");

                            // Publicar feedback automáticamente
                            PublicarFeedback(topicControl, topicFeedback, valorActual);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error procesando cambios: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Publica un valor en el topic de feedback correspondiente
        /// </summary>
        private async void PublicarFeedback(string topicControl, string topicFeedback, string valor)
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📤 SINÓPTICO: Publicando feedback...\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📤 SINÓPTICO: Control: '{topicControl}' → Feedback: '{topicFeedback}'\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📤 SINÓPTICO: Valor: '{valor}'\n");

                // Publicar en el topic de feedback
                bool publicado = await mqtt.EscribirTopic(topicFeedback, valor);

                if (publicado)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Feedback publicado exitosamente\n");

                    // Programar verificación de confirmación
                    Task.Delay(1000).ContinueWith(_ => VerificarConfirmacionFeedback(topicFeedback, valor));
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error publicando feedback\n");
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error en publicación: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Verifica que el feedback fue recibido correctamente
        /// </summary>
        private void VerificarConfirmacionFeedback(string topicFeedback, string valorEsperado)
        {
            try
            {
                if (!sinopticoHabilitado || mqtt == null || !mqtt.EstaConectado)
                    return;

                string? valorRecibido = mqtt.LeerTopic(topicFeedback);

                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        if (valorRecibido == valorEsperado)
                        {
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Confirmación exitosa en '{topicFeedback}'\n");
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Valor confirmado: '{valorRecibido}'\n");
                        }
                        else
                        {
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: Confirmación pendiente en '{topicFeedback}'\n");
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: Esperado: '{valorEsperado}', Recibido: '{valorRecibido ?? "null"}'\n");
                        }
                        rtbLog.ScrollToCaret();
                    }));
                }
            }
            catch (Exception ex)
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error verificando confirmación: {ex.Message}\n");
                        rtbLog.ScrollToCaret();
                    }));
                }
            }
        }

        private void panelLog_Paint(object sender, PaintEventArgs e)
        {

        }

        #endregion

        #region Eventos Modbus

        /// <summary>
        /// Evento del botón Conectar Modbus
        /// </summary>
        private async void btnModbusConectar_Click(object sender, EventArgs e)
        {
            try
            {
                // Actualizar indicador de estado
                lblEstadoModbus.Text = "🔄 Conectando...";
                lblEstadoModbus.ForeColor = Color.Yellow;

                // Crear nueva conexión Modbus
                modbus = new ConexionModbus(
                    txtModbusIp.Text.Trim(),
                    (int)nudModbusPuerto.Value,
                    (byte)nudModbusDeviceId.Value,
                    5000
                );

                // Configurar eventos
                modbus.Conectado += (s, args) =>
                {
                    if (InvokeRequired)
                    {
                        Invoke(new Action(() =>
                        {
                            lblEstadoModbus.Text = "🟢 Modbus: Conectado";
                            lblEstadoModbus.ForeColor = Color.LimeGreen;
                            btnModbusConectar.Enabled = false;
                            btnModbusDesconectar.Enabled = true;
                            btnModbusLeerRegistros.Enabled = true;
                            btnModbusEscribirRegistro.Enabled = true;

                            // LOG DE DEPURACIÓN - BOTONES HABILITADOS
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔧 DEBUG: Botones de lectura/escritura HABILITADOS\n");
                            rtbLog.ScrollToCaret();
                        }));
                    }
                };

                modbus.Desconectado += (s, args) =>
                {
                    if (InvokeRequired)
                    {
                        Invoke(new Action(() =>
                        {
                            lblEstadoModbus.Text = "🔴 Modbus: Desconectado";
                            lblEstadoModbus.ForeColor = Color.Red;
                            btnModbusConectar.Enabled = true;
                            btnModbusDesconectar.Enabled = false;
                            //btnModbusLeerRegistros.Enabled = false;
                            btnModbusEscribirRegistro.Enabled = false;
                        }));
                    }
                };

                modbus.ErrorOcurrido += (s, mensaje) =>
                {
                    if (InvokeRequired)
                    {
                        Invoke(new Action(() =>
                        {
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS ERROR: {mensaje}\n");
                            rtbLog.ScrollToCaret();
                        }));
                    }
                };

                // Conectar
                bool conectado = await modbus.ConectarAsync();

                if (conectado)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Conectado exitosamente a {txtModbusIp.Text}:{nudModbusPuerto.Value}\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔧 MODBUS: Device ID {nudModbusDeviceId.Value}\n");
                }
                else
                {
                    lblEstadoModbus.Text = "🔴 Modbus: Error";
                    lblEstadoModbus.ForeColor = Color.Red;
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error en la conexión\n");
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                lblEstadoModbus.Text = "🔴 Modbus: Error";
                lblEstadoModbus.ForeColor = Color.Red;
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error conectando - {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Evento del botón Desconectar Modbus
        /// </summary>
        private void btnModbusDesconectar_Click(object sender, EventArgs e)
        {
            try
            {
                modbus?.Desconectar();
                modbus?.Dispose();
                modbus = null;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Desconectado correctamente\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error desconectando - {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Evento del botón Leer Registros Modbus
        /// </summary>
        private async void btnModbusLeerRegistros_Click(object sender, EventArgs e)
        {
            // LOG DE DEPURACIÓN - INICIO DEL EVENTO
            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🚀 DEBUG: Evento btnModbusLeerRegistros_Click INICIADO\n");
            rtbLog.ScrollToCaret();

            try
            {
                // LOG DE DEPURACIÓN - VERIFICAR CONEXIÓN
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 DEBUG: Verificando conexión Modbus...\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 DEBUG: modbus == null: {modbus == null}\n");
                if (modbus != null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 DEBUG: modbus.EstaConectado: {modbus.EstaConectado}\n");
                }
                rtbLog.ScrollToCaret();

                if (modbus == null || !modbus.EstaConectado)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ DEBUG: No hay conexión Modbus activa\n");
                    MessageBox.Show("❌ No hay conexión Modbus activa", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtModbusDireccionRegistro.Text))
                {
                    MessageBox.Show("❌ Ingrese la dirección del registro", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Convertir dirección MW a Holding Register si es necesario
                ushort direccion = ConvertirDireccionModbus(txtModbusDireccionRegistro.Text.Trim());
                if (direccion == 0)
                {
                    MessageBox.Show("❌ Dirección de registro inválida\n\nEjemplos válidos:\n- MW430 (se convierte a 40431)\n- 40431 (dirección Modbus directa)", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Convertir dirección Modbus a dirección base 0 para el protocolo
                ushort direccionProtocolo = (ushort)(direccion);

                // Intentar leer como Holding Register primero
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 MODBUS: Intentando leer Holding Register {direccion} (protocolo: {direccionProtocolo})...\n");
                rtbLog.ScrollToCaret();

                ushort[]? valores = null;
                bool exitoso = false;
                string tipoRegistro = "";

                try
                {
                    valores = await modbus.LeerHoldingRegistersAsync(direccionProtocolo, 1);
                    exitoso = true;
                    tipoRegistro = "Holding Register";
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Éxito leyendo como Holding Register\n");
                }
                catch (Exception exHolding)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error en Holding Register - {exHolding.Message}\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Intentando como Input Register...\n");
                    rtbLog.ScrollToCaret();

                    try
                    {
                        valores = await modbus.LeerInputRegistersAsync(direccionProtocolo, 1);
                        exitoso = true;
                        tipoRegistro = "Input Register";
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Éxito leyendo como Input Register\n");
                    }
                    catch (Exception exInput)
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error en Input Register - {exInput.Message}\n");
                        throw new Exception($"No se pudo leer como Holding Register ni Input Register. Holding: {exHolding.Message}, Input: {exInput.Message}");
                    }
                }

                if (exitoso && valores != null && valores.Length > 0)
                {
                    ushort valor = valores[0];

                    // Actualizar el label con el valor leído
                    lblModbusValorLeido.Text = $"Valor: {valor}";
                    lblModbusValorLeido.ForeColor = Color.LimeGreen;

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 MODBUS: {tipoRegistro} {direccion} = {valor} (0x{valor:X4}) [Decimal: {valor}]\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 💡 VALOR MOSTRADO EN LABEL: {valor}\n");

                    // MOSTRAR MESSAGEBOX CON EL VALOR LEÍDO
                    MessageBox.Show(
                        $"✅ VALOR LEÍDO EXITOSAMENTE\n\n" +
                        $"📍 Dirección: {direccion}\n" +
                        $"📊 Tipo: {tipoRegistro}\n" +
                        $"🔢 Valor Decimal: {valor}\n" +
                        $"🔢 Valor Hexadecimal: 0x{valor:X4}\n" +
                        $"🔢 Valor Binario: {Convert.ToString(valor, 2).PadLeft(16, '0')}\n\n" +
                        $"PLC: {(modbus?.EstaConectado == true ? "Conectado" : "Desconectado")}",
                        "🎉 LECTURA MODBUS EXITOSA",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information
                    );
                }
                else
                {
                    lblModbusValorLeido.Text = "Valor: Sin datos";
                    lblModbusValorLeido.ForeColor = Color.Orange;
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ MODBUS: No se recibieron datos válidos\n");

                    // MOSTRAR MESSAGEBOX DE ERROR
                    MessageBox.Show(
                        "❌ NO SE RECIBIERON DATOS VÁLIDOS\n\n" +
                        "Posibles causas:\n" +
                        "• Dirección inexistente en el PLC\n" +
                        "• PLC no responde\n" +
                        "• Configuración incorrecta\n" +
                        "• Problema de comunicación",
                        "⚠️ ERROR DE LECTURA MODBUS",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                // Mostrar error en el label
                lblModbusValorLeido.Text = "Valor: ERROR";
                lblModbusValorLeido.ForeColor = Color.Red;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error leyendo registro - {ex.Message}\n");
                rtbLog.ScrollToCaret();

                // MOSTRAR MESSAGEBOX DE ERROR DETALLADO
                MessageBox.Show(
                    $"❌ ERROR AL LEER REGISTRO MODBUS\n\n" +
                    $"📍 Dirección solicitada: {txtModbusDireccionRegistro.Text}\n" +
                    $"🔌 Estado conexión: {(modbus?.EstaConectado == true ? "Conectado" : "Desconectado")}\n\n" +
                    $"💥 Error detallado:\n{ex.Message}\n\n" +
                    $"🔧 Soluciones:\n" +
                    $"• Verificar que el PLC esté conectado\n" +
                    $"• Comprobar la dirección del registro\n" +
                    $"• Verificar Device ID (actual: {nudModbusDeviceId.Value})\n" +
                    $"• Revisar configuración de red",
                    "💥 ERROR DE COMUNICACIÓN MODBUS",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// Evento del botón Escribir Registro Modbus
        /// </summary>
        private async void btnModbusEscribirRegistro_Click(object sender, EventArgs e)
        {
            try
            {
                if (modbus == null || !modbus.EstaConectado)
                {
                    MessageBox.Show("❌ No hay conexión Modbus activa", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtModbusDireccionRegistro.Text))
                {
                    MessageBox.Show("❌ Ingrese la dirección del registro", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtModbusValorEscritura.Text))
                {
                    MessageBox.Show("❌ Ingrese el valor a escribir", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Convertir dirección MW a Holding Register si es necesario
                ushort direccion = ConvertirDireccionModbus(txtModbusDireccionRegistro.Text.Trim());
                if (direccion == 0)
                {
                    MessageBox.Show("❌ Dirección de registro inválida\n\nEjemplos válidos:\n- MW430 (se convierte a 40431)\n- 40431 (dirección Modbus directa)", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!ushort.TryParse(txtModbusValorEscritura.Text, out ushort valor))
                {
                    MessageBox.Show("❌ Valor de escritura inválido", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Convertir dirección Modbus a dirección base 0 para el protocolo
                ushort direccionProtocolo = (ushort)(direccion - 40001);

                // Escribir registro
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✏️ MODBUS: Escribiendo registro {direccion} (protocolo: {direccionProtocolo}) = {valor}...\n");

                await modbus.EscribirHoldingRegisterAsync(direccionProtocolo, valor);

                // Actualizar el label para mostrar que se escribió correctamente
                lblModbusValorLeido.Text = $"Escrito: {valor}";
                lblModbusValorLeido.ForeColor = Color.Orange;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Registro {direccion} escrito exitosamente\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                // Mostrar error en el label
                lblModbusValorLeido.Text = "Valor: ERROR";
                lblModbusValorLeido.ForeColor = Color.Red;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error escribiendo registro - {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Convierte direcciones MW de Schneider a direcciones Modbus Holding Register
        /// </summary>
        /// <param name="direccionTexto">Dirección como texto (MW430 o 40431)</param>
        /// <returns>Dirección Modbus válida o 0 si es inválida</returns>
        private ushort ConvertirDireccionModbus(string direccionTexto)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(direccionTexto))
                    return 0;

                direccionTexto = direccionTexto.ToUpper().Trim();

                // Si es formato MW (Memory Word de Schneider)
                if (direccionTexto.StartsWith("MW"))
                {
                    string numeroStr = direccionTexto.Substring(2);
                    if (ushort.TryParse(numeroStr, out ushort numeroMW))
                    {
                        // MW430 se convierte a Holding Register 40431 (MW + 40001)
                        ushort direccionModbus = (ushort)(numeroMW + 40001);
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Convertido {direccionTexto} → Holding Register {direccionModbus} (protocolo: {numeroMW})\n");
                        return direccionModbus;
                    }
                }
                // Si es dirección Modbus directa (40001-49999)
                else if (ushort.TryParse(direccionTexto, out ushort direccionDirecta))
                {
                    if (direccionDirecta >= 40001 && direccionDirecta <= 49999)
                    {
                        return direccionDirecta;
                    }
                }

                return 0; // Dirección inválida
            }
            catch
            {
                return 0;
            }
        }



        #endregion

        // FIN GESTIÓN DE LA NAVEGACIÓN ************************************************************************
    }
}
