﻿using ControlDeProducciónENAGAS.Clases;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ControlDeProducciónENAGAS.Formularios
{
    public partial class MAIN : Form
    {
        public MAIN()
        {
            InitializeComponent();

            // Configura el estado inicial del formulario
            ConfigurarEstadoInicial();
        }




        // GESTIÓN DE LA NAVEGACIÓN Y DISEÑO ***************************************************************

        /// <summary>
        /// Método central para cambiar de pestaña y actualizar el estilo de los botones.
        /// </summary>
        /// <param name="paginaDestino">La TabPage que se va a mostrar.</param>
        /// <param name="botonActivo">El botón de navegación que se va a resaltar.</param>
        /// 
        private void ConfigurarEstadoInicial()
        {
            // Opcional: Oculta las cabeceras de las pestañas para que el usuario solo navegue con los botones.
            // Si quieres ver las pestañas, comenta o elimina las siguientes 3 líneas.
            tabControlPrincipal.Appearance = TabAppearance.FlatButtons;
            tabControlPrincipal.ItemSize = new Size(0, 1);
            tabControlPrincipal.SizeMode = TabSizeMode.Fixed;

            // Establece la pestaña de Correo como la inicial
            NavegarHacia(tabPageCorreo, btnNavCorreo);
        }
        private void NavegarHacia(TabPage paginaDestino, Button botonActivo)
        {
            // Cambia la pestaña activa en el TabControl
            if (tabControlPrincipal.SelectedTab != paginaDestino)
            {
                tabControlPrincipal.SelectedTab = paginaDestino;
            }

            // Actualiza el estilo de los botones
            ResetearEstilosBotones();
            ResaltarBoton(botonActivo);
        }

        /// <summary>
        /// Pone todos los botones de navegación en su estado "inactivo".
        /// </summary>
        private void ResetearEstilosBotones()
        {
            Color colorInactivo = Color.FromArgb(60, 63, 65);
            Font fuenteNormal = new Font("Segoe UI", 12F, FontStyle.Regular);

            btnNavCorreo.BackColor = colorInactivo;
            btnNavCorreo.Font = fuenteNormal;
            btnNavMqtt.BackColor = colorInactivo;
            btnNavMqtt.Font = fuenteNormal;
            btnNavModbus.BackColor = colorInactivo;
            btnNavModbus.Font = fuenteNormal;
            btnNavSinoptico.BackColor = colorInactivo;
            btnNavSinoptico.Font = fuenteNormal;
        }

        /// <summary>
        /// Resalta un botón de navegación para mostrar que está "activo".
        /// </summary>
        private void ResaltarBoton(Button boton)
        {
            boton.BackColor = Color.FromArgb(0, 122, 204);
            boton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
        }


        // --- EVENTOS CLICK DE LOS BOTONES DE NAVEGACIÓN ---

        private void btnNavCorreo_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageCorreo, btnNavCorreo);
        }

        private void btnNavMqtt_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageMqtt, btnNavMqtt);
        }

        private void btnNavModbus_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageModbus, btnNavModbus);
        }

        private void btnNavSinoptico_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageSinoptico, btnNavSinoptico);
        }
        private void btnCerrar_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        private void btnLimpiarLog_Click(object sender, EventArgs e)
        {
            rtbLog.Clear();
            rtbLog.AppendText("=== LOG DEL SISTEMA (limpiado) ===\n");
        }


        private async void btnMqttConectar_Click(object sender, EventArgs e)
        {
            try
            {
                // Crear instancia de ConexionMQTT con los valores del formulario
                var mqtt = new ConexionMQTT
                {
                    Protocolo = cmbMqttProtocolo.SelectedItem?.ToString() ?? "mqtt://",
                    Host = txtMqttHost.Text,
                    Puerto = int.TryParse(txtMqttPuerto.Text, out int puerto) ? puerto : 1883,
                    ClientId = txtMqttClientId.Text,
                    Usuario = txtMqttUsuario.Text,
                    Password = txtMqttPassword.Text,
                    UsarSslTls = chkMqttSslTls.Checked
                };

                // Validar configuración
                if (!mqtt.EsConfiguracionValida())
                {
                    MessageBox.Show("❌ Configuración MQTT no válida. Revisa los campos.",
                                   "Error MQTT", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Deshabilitar botón mientras conecta
                btnMqttConectar.Enabled = false;
                btnMqttConectar.Text = "Conectando...";

                // Intentar conectar
                bool conectado = await mqtt.ConectarAsync();

                if (conectado)
                {
                    // Actualizar interfaz
                    lblEstadoMqtt.Text = mqtt.EstadoTexto;
                    panelEstadoMqtt.BackColor = Color.Green;
                    btnMqttConectar.Enabled = false;
                    btnMqttDesconectar.Enabled = true;

                    // Guardar referencia global para usar en timer
                    _conexionMqtt = mqtt;

                    // Cargar topics del CSV y suscribirse
                    await CargarYSuscribirTopics();

                    // Iniciar timer para lectura periódica
                    _timerLecturaTopics.Start();

                    MessageBox.Show("✅ Conectado exitosamente a MQTT",
                                   "Conexión MQTT", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("❌ No se pudo conectar al broker MQTT",
                                   "Error MQTT", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    btnMqttConectar.Enabled = true;
                    btnMqttConectar.Text = "Conectar";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ Error al conectar: {ex.Message}",
                               "Error MQTT", MessageBoxButtons.OK, MessageBoxIcon.Error);
                btnMqttConectar.Enabled = true;
                btnMqttConectar.Text = "Conectar";
            }
        }


        // FIN GESTIÓN DE LA NAVEGACIÓN ************************************************************************
    }
}
