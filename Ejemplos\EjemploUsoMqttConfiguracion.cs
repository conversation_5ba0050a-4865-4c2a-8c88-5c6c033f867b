using System;
using System.Windows.Forms;
using ControlDeProducciónENAGAS.Clases;
using ControlDeProducciónENAGAS.Formularios;

namespace ControlDeProducciónENAGAS.Ejemplos
{
    /// <summary>
    /// Ejemplo de cómo usar las clases MqttConfiguracion y MqttFormHelper
    /// con el formulario MAIN
    /// </summary>
    public static class EjemploUsoMqttConfiguracion
    {
        /// <summary>
        /// Ejemplo 1: Extraer configuración desde el formulario
        /// </summary>
        /// <param name="formularioMain">Instancia del formulario MAIN</param>
        public static void EjemploExtraerConfiguracion(MAIN formularioMain)
        {
            try
            {
                // Extraer la configuración actual del formulario
                var config = MqttFormHelper.ExtraerConfiguracionDesdeFormulario(formularioMain);
                
                // Mostrar información de la configuración
                MessageBox.Show($"📋 Configuración MQTT extraída:\n\n" +
                               $"🌐 Protocolo: {config.Protocolo}\n" +
                               $"🖥️ Host: {config.Host}\n" +
                               $"🔌 Puerto: {config.Puerto}\n" +
                               $"🆔 Client ID: {config.ClientId}\n" +
                               $"👤 Usuario: {config.Usuario}\n" +
                               $"🔒 SSL/TLS: {(config.UsarSslTls ? "Sí" : "No")}\n" +
                               $"🔗 URL Completa: {config.UrlCompleta}\n" +
                               $"✅ Válida: {(config.EsConfiguracionValida() ? "Sí" : "No")}",
                               "Configuración MQTT", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ Error al extraer configuración: {ex.Message}", 
                               "Error", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Ejemplo 2: Crear y aplicar una configuración predefinida
        /// </summary>
        /// <param name="formularioMain">Instancia del formulario MAIN</param>
        public static void EjemploAplicarConfiguracionPredefinida(MAIN formularioMain)
        {
            try
            {
                // Crear configuración para servidor MQTT local
                var configLocal = new MqttConfiguracion
                {
                    Protocolo = "mqtt://",
                    Host = "localhost",
                    Puerto = 1883,
                    Usuario = "enagas_user",
                    Password = "enagas_pass",
                    UsarSslTls = false
                };

                // Aplicar al formulario
                MqttFormHelper.AplicarConfiguracionAlFormulario(formularioMain, configLocal);
                
                MessageBox.Show("✅ Configuración local aplicada correctamente", 
                               "Configuración MQTT", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ Error al aplicar configuración: {ex.Message}", 
                               "Error", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Ejemplo 3: Configuración para servidor MQTT seguro
        /// </summary>
        /// <param name="formularioMain">Instancia del formulario MAIN</param>
        public static void EjemploConfiguracionSegura(MAIN formularioMain)
        {
            try
            {
                // Crear configuración para servidor MQTT seguro
                var configSegura = new MqttConfiguracion
                {
                    Protocolo = "mqtts://",
                    Host = "mqtt.enagas.es",
                    Puerto = 8883,
                    Usuario = "produccion_user",
                    Password = "super_secure_password",
                    UsarSslTls = true
                };

                // Validar antes de aplicar
                if (MqttFormHelper.ValidarConfiguracion(configSegura))
                {
                    MqttFormHelper.AplicarConfiguracionAlFormulario(formularioMain, configSegura);
                    MessageBox.Show("🔒 Configuración segura aplicada correctamente", 
                                   "Configuración MQTT", 
                                   MessageBoxButtons.OK, 
                                   MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ Error al aplicar configuración segura: {ex.Message}", 
                               "Error", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Ejemplo 4: Validar configuración actual
        /// </summary>
        /// <param name="formularioMain">Instancia del formulario MAIN</param>
        public static void EjemploValidarConfiguracion(MAIN formularioMain)
        {
            try
            {
                var config = MqttFormHelper.ExtraerConfiguracionDesdeFormulario(formularioMain);
                
                if (MqttFormHelper.ValidarConfiguracion(config, false))
                {
                    MessageBox.Show("✅ La configuración actual es válida y está lista para conectar", 
                                   "Validación MQTT", 
                                   MessageBoxButtons.OK, 
                                   MessageBoxIcon.Information);
                }
                else
                {
                    // Mostrar errores específicos
                    var errores = config.ObtenerErroresValidacion();
                    var mensaje = "⚠️ La configuración tiene errores:\n\n" + string.Join("\n", errores);
                    MessageBox.Show(mensaje, 
                                   "Validación MQTT", 
                                   MessageBoxButtons.OK, 
                                   MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ Error al validar configuración: {ex.Message}", 
                               "Error", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Ejemplo 5: Exportar configuración actual
        /// </summary>
        /// <param name="formularioMain">Instancia del formulario MAIN</param>
        public static void EjemploExportarConfiguracion(MAIN formularioMain)
        {
            try
            {
                var config = MqttFormHelper.ExtraerConfiguracionDesdeFormulario(formularioMain);
                var json = MqttFormHelper.ExportarConfiguracion(config);
                
                // Mostrar JSON en un cuadro de diálogo
                var form = new Form
                {
                    Text = "Configuración MQTT - JSON",
                    Size = new System.Drawing.Size(500, 400),
                    StartPosition = FormStartPosition.CenterParent
                };

                var textBox = new TextBox
                {
                    Multiline = true,
                    ScrollBars = ScrollBars.Both,
                    Dock = DockStyle.Fill,
                    Text = json,
                    ReadOnly = true,
                    Font = new System.Drawing.Font("Consolas", 10)
                };

                form.Controls.Add(textBox);
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ Error al exportar configuración: {ex.Message}", 
                               "Error", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Ejemplo 6: Simular cambio de estado de conexión
        /// </summary>
        /// <param name="formularioMain">Instancia del formulario MAIN</param>
        /// <param name="conectado">Estado de conexión a simular</param>
        public static void EjemploActualizarEstado(MAIN formularioMain, bool conectado)
        {
            try
            {
                var config = MqttFormHelper.ExtraerConfiguracionDesdeFormulario(formularioMain);
                config.EstaConectado = conectado;
                
                MqttFormHelper.ActualizarEstadoConexion(formularioMain, config);
                
                MessageBox.Show($"🔄 Estado actualizado: {config.EstadoTexto}", 
                               "Estado MQTT", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ Error al actualizar estado: {ex.Message}", 
                               "Error", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Ejemplo 7: Configuración con eventos
        /// </summary>
        /// <param name="formularioMain">Instancia del formulario MAIN</param>
        public static void EjemploConfiguracionConEventos(MAIN formularioMain)
        {
            try
            {
                var config = new MqttConfiguracion();
                
                // Suscribirse a cambios de propiedades
                config.PropertyChanged += (sender, e) =>
                {
                    Console.WriteLine($"🔔 Propiedad cambiada: {e.PropertyName}");
                    
                    // Actualizar formulario cuando cambie la configuración
                    if (sender is MqttConfiguracion cfg)
                    {
                        MqttFormHelper.ActualizarEstadoConexion(formularioMain, cfg);
                    }
                };

                // Hacer algunos cambios para demostrar los eventos
                config.Host = "test.mosquitto.org";
                config.Puerto = 1883;
                config.EstaConectado = true;
                
                MessageBox.Show("📡 Configuración con eventos creada. Revisa la consola para ver los cambios.", 
                               "Eventos MQTT", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ Error con eventos: {ex.Message}", 
                               "Error", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Error);
            }
        }
    }
}
