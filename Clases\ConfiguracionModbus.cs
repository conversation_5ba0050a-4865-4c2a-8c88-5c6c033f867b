using System.Text.Json;

namespace ControlDeProducciónENAGAS.Clases
{
    /// <summary>
    /// Clase para manejar la configuración de Modbus
    /// </summary>
    public class ConfiguracionModbus
    {
        public string Name { get; set; } = "";
        public string Ip { get; set; } = "*************";
        public int Puerto { get; set; } = 502;
        public byte DeviceId { get; set; } = 1;
        public int Timeout { get; set; } = 5000;

        /// <summary>
        /// Constructor por defecto
        /// </summary>
        public ConfiguracionModbus() { }

        /// <summary>
        /// Constructor con parámetros
        /// </summary>
        public ConfiguracionModbus(string name, string ip, int puerto, byte deviceId, int timeout = 5000)
        {
            Name = name;
            Ip = ip;
            Puerto = puerto;
            DeviceId = deviceId;
            Timeout = timeout;
        }

        /// <summary>
        /// Carga la configuración desde archivo JSON
        /// </summary>
        /// <param name="rutaArchivo">Ruta del archivo</param>
        /// <returns>Configuración cargada o configuración por defecto</returns>
        public static ConfiguracionModbus CargarDesdeArchivo(string rutaArchivo = "config_modbus.json")
        {
            try
            {
                if (!File.Exists(rutaArchivo))
                {
                    // Crear configuración por defecto
                    var configDefault = new ConfiguracionModbus
                    {
                        Name = "PLC ENAGAS Huelva",
                        Ip = "*************",
                        Puerto = 502,
                        DeviceId = 1,
                        Timeout = 5000
                    };
                    
                    // Guardar configuración por defecto
                    GuardarEnArchivo(configDefault, rutaArchivo);
                    return configDefault;
                }

                string jsonString = File.ReadAllText(rutaArchivo);
                var jsonDocument = JsonDocument.Parse(jsonString);
                var modbusElement = jsonDocument.RootElement.GetProperty("modbus");

                return new ConfiguracionModbus
                {
                    Name = modbusElement.GetProperty("name").GetString() ?? "",
                    Ip = modbusElement.GetProperty("ip").GetString() ?? "*************",
                    Puerto = modbusElement.GetProperty("puerto").GetInt32(),
                    DeviceId = (byte)modbusElement.GetProperty("deviceId").GetInt32(),
                    Timeout = modbusElement.GetProperty("timeout").GetInt32()
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"❌ Error cargando configuración Modbus: {ex.Message}");
            }
        }

        /// <summary>
        /// Guarda la configuración en archivo JSON
        /// </summary>
        /// <param name="config">Configuración a guardar</param>
        /// <param name="rutaArchivo">Ruta del archivo</param>
        public static void GuardarEnArchivo(ConfiguracionModbus config, string rutaArchivo = "config_modbus.json")
        {
            try
            {
                var jsonObject = new
                {
                    modbus = new
                    {
                        name = config.Name,
                        ip = config.Ip,
                        puerto = config.Puerto,
                        deviceId = config.DeviceId,
                        timeout = config.Timeout
                    }
                };

                string jsonString = JsonSerializer.Serialize(jsonObject, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                File.WriteAllText(rutaArchivo, jsonString);
            }
            catch (Exception ex)
            {
                throw new Exception($"❌ Error guardando configuración Modbus: {ex.Message}");
            }
        }

        /// <summary>
        /// Representación en string de la configuración
        /// </summary>
        public override string ToString()
        {
            return $"Modbus TCP - {Name} ({Ip}:{Puerto}) - Device ID: {DeviceId}";
        }
    }
}
