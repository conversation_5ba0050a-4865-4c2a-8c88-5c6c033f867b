# 📡 Clase ConexionMQTT - Control de Producción ENAGAS

## 📋 Descripción
La clase `ConexionMQTT` encapsula todos los parámetros de configuración MQTT del panel MQTT del formulario MAIN y proporciona métodos para conectar, leer y escribir topics MQTT.

## 🚀 Características Principales
- ✅ Configuración completa de parámetros MQTT
- 🔌 Conexión y desconexión automática
- 📤 Publicación de mensajes en topics
- 📥 Lectura de valores de topics
- 🔔 Suscripción a topics
- 🛡️ Validación de configuración
- 🔒 Soporte para SSL/TLS

## 📦 Parámetros de Configuración

### Propiedades Principales
```csharp
public string Protocolo { get; set; }     // mqtt://, mqtts://, ws://, wss://
public string Host { get; set; }          // Dirección del servidor
public int Puerto { get; set; }           // Puerto del servidor (1883, 8883)
public string ClientId { get; set; }      // ID único del cliente
public string Usuario { get; set; }       // Usuario para autenticación
public string Password { get; set; }      // Contraseña
public bool UsarSslTls { get; set; }      // Usar conexión segura
public bool EstaConectado { get; }        // Estado de conexión (solo lectura)
```

### Propiedades Calculadas
```csharp
public string EstadoTexto { get; }        // "MQTT: Conectado ✅" / "MQTT: Desconectado ❌"
public string UrlCompleta { get; }        // "mqtt://localhost:1883"
```

## 🔧 Métodos Principales

### 1. Conexión
```csharp
// Conectar al broker MQTT
public async Task<bool> ConectarAsync()

// Desconectar del broker
public void Desconectar()

// Validar configuración
public bool EsConfiguracionValida()
```

### 2. Operaciones con Topics
```csharp
// Escribir/Publicar en un topic
public async Task<bool> EscribirTopic(string topic, string mensaje)

// Leer último valor de un topic
public string? LeerTopic(string topic)

// Suscribirse a un topic
public async Task<bool> SuscribirTopic(string topic)
```

### 3. Utilidades
```csharp
// Generar Client ID aleatorio
public void GenerarClientIdAleatorio()

// Representación en string
public override string ToString()
```

## 💡 Ejemplos de Uso

### Ejemplo Básico
```csharp
// Crear instancia
var mqtt = new ConexionMQTT
{
    Host = "localhost",
    Puerto = 1883,
    Protocolo = "mqtt://",
    ClientId = "ENAGAS_Client_001"
};

// Conectar
if (await mqtt.ConectarAsync())
{
    // Suscribirse a un topic
    await mqtt.SuscribirTopic("enagas/temperatura");
    
    // Publicar un mensaje
    await mqtt.EscribirTopic("enagas/temperatura", "25.5°C");
    
    // Leer valor (después de un tiempo)
    await Task.Delay(1000);
    string? temperatura = mqtt.LeerTopic("enagas/temperatura");
    
    // Desconectar
    mqtt.Desconectar();
}
```

### Ejemplo con Autenticación
```csharp
var mqtt = new ConexionMQTT
{
    Host = "mqtt.enagas.es",
    Puerto = 8883,
    Protocolo = "mqtts://",
    Usuario = "produccion_user",
    Password = "secure_password",
    UsarSslTls = true
};

if (await mqtt.ConectarAsync())
{
    Console.WriteLine($"✅ {mqtt.EstadoTexto}");
    // ... operaciones ...
}
```

### Ejemplo de Monitoreo Continuo
```csharp
var mqtt = new ConexionMQTT("localhost", 1883);

if (await mqtt.ConectarAsync())
{
    // Suscribirse a múltiples topics
    await mqtt.SuscribirTopic("enagas/alarmas");
    await mqtt.SuscribirTopic("enagas/eventos");
    
    // Monitoreo continuo
    while (mqtt.EstaConectado)
    {
        string? alarma = mqtt.LeerTopic("enagas/alarmas");
        if (!string.IsNullOrEmpty(alarma))
            Console.WriteLine($"🚨 ALARMA: {alarma}");
            
        await Task.Delay(1000);
    }
}
```

## 🔧 Integración con Formulario MAIN

### Extraer Configuración del Formulario
```csharp
// En el formulario MAIN, crear método para obtener configuración
public ConexionMQTT ObtenerConfiguracionMQTT()
{
    return new ConexionMQTT
    {
        Protocolo = cmbMqttProtocolo.SelectedItem?.ToString() ?? "mqtt://",
        Host = txtMqttHost.Text,
        Puerto = int.TryParse(txtMqttPuerto.Text, out int puerto) ? puerto : 1883,
        ClientId = txtMqttClientId.Text,
        Usuario = txtMqttUsuario.Text,
        Password = txtMqttPassword.Text,
        UsarSslTls = chkMqttSslTls.Checked
    };
}
```

### Aplicar Configuración al Formulario
```csharp
public void AplicarConfiguracionMQTT(ConexionMQTT config)
{
    cmbMqttProtocolo.SelectedItem = config.Protocolo;
    txtMqttHost.Text = config.Host;
    txtMqttPuerto.Text = config.Puerto.ToString();
    txtMqttClientId.Text = config.ClientId;
    txtMqttUsuario.Text = config.Usuario;
    txtMqttPassword.Text = config.Password;
    chkMqttSslTls.Checked = config.UsarSslTls;
    
    // Actualizar estado visual
    lblEstadoMqtt.Text = config.EstadoTexto;
}
```

## ⚠️ Consideraciones Importantes

### Validación
- Siempre validar la configuración antes de conectar: `config.EsConfiguracionValida()`
- El Host no puede estar vacío
- El Puerto debe estar entre 1 y 65535
- El ClientId debe ser único

### Manejo de Errores
- Los métodos async pueden lanzar excepciones
- Usar try-catch para manejar errores de conexión
- Verificar `EstaConectado` antes de operaciones

### Rendimiento
- La escucha de mensajes se ejecuta en background
- Los valores de topics se almacenan en memoria
- Desconectar cuando no se necesite la conexión

## 🔒 Seguridad
- Usar `mqtts://` para conexiones seguras
- Configurar `UsarSslTls = true` para SSL/TLS
- No hardcodear credenciales en el código
- Usar variables de entorno o configuración externa

## 📁 Archivos Relacionados
- `Clases/MqttConfiguracion.cs` - Clase principal
- `Ejemplos/EjemploConexionMQTT.cs` - Ejemplos de uso
- `Formularios/MAIN.cs` - Formulario principal con controles MQTT

## 🎯 Casos de Uso Típicos en ENAGAS
1. **Monitoreo de Producción**: Leer datos de sensores
2. **Control de Procesos**: Enviar comandos a equipos
3. **Alertas**: Recibir y procesar alarmas
4. **Logging**: Publicar eventos del sistema
5. **Sincronización**: Coordinar entre sistemas

¡La clase está lista para usar! 🚀
